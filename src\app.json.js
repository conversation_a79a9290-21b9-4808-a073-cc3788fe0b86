/*
 * @Date: 2021-12-29 19:04:26
 * @Description: 该文件对应的是 app.json，采用 js 写法可以使用环境变量 ENV_IS_SWAN 等，可以执行js，但是不能引入其他文件。
 */

/** 处理百度SEO */
const wid = { backgroundColor: "#f8f8f8" };
const sitemapSett = {};
const swanPages = [];
if (ENV_IS_SWAN) {
  wid.backgroundColor = "#00000000";
  sitemapSett["dynamicLib"] = {
    // 'swan-sitemap-lib' 是个可自己定的别名。
    "swan-sitemap-lib": {
      // provider 是要引用的动态库的名字，在此为 'swan-sitemap'。
      provider: "swan-sitemap"
    }
  };
  sitemapSett["useSwanNews"] = true;
  swanPages.push("swan-sitemap/resume/index");
  swanPages.push("swan-sitemap/recruit/index");
}

/** @name 百度seo优化页面 */
const swanrecruitseo = [
  {
    // 工厂首页（工厂招工大列表）
    root: "swanrecruitseo",
    pages: [
      //招工大全
      "recruitListSeo/index",
      //招工大全详情
      "recruitListDetailSeo/index",
      //结构化聚合页
      "seoAggregation/index"
    ]
  }
];

const usingComponents = ENV_IS_WEAPP
  ? {
      // "c-m": "/components/widget/modal/index", // 弹窗
      "c-m": "/subpackage/components/index/widget/modal/index", // 弹窗
      "custom-header": "/components/base/custom-header/index", // 自定义头部
      a: "/components/base/a/index", // a 组件
      "icon-font": "/components/base/icon-font/index", // 图标组件
      "setting-tools": "/components/base/setting-tools/index", // 设置工具
      "v5-privacy-protocol": "/components/widget/v5-privacy-protocol/index", // 隐私协议弹窗
      "ab-slot": "/components/base/ab-slot/index"
    }
  : {};

/** 根据不同小程序设置不同的入口 */
const entryPagePath = (() => {
  // 工地活--鱼泡招工
  if (ENV_SUB === "gdh") {
    return "subpackage/recruit/fast_issue/index/index";
  }
  // 默认首页
  return "pages/index/index";
})();

/** 根据不同小程序设置不同的权限 */
const permission = (() => {
  // 物流-招司机｜搬运工
  if (ENV_SUB === "zsjbyg") {
    return {
      "scope.userFuzzyLocation": {
        desc: "你的位置信息将用于小程序为您自动推荐位置信息"
      }
    };
  }
  // 默认首页
  return {
    "scope.userLocation": {
      desc: "你的位置信息将用于小程序为您自动推荐位置信息"
    }
  };
})();

/** 根据不同小程序使用不同的api */
const requiredPrivateInfos = (() => {
  // 物流-招司机｜搬运工
  if (ENV_SUB === "zsjbyg") {
    // 获取模糊地理位置
    return ["getFuzzyLocation"];
  }
  // 默认首页
  return ["getLocation"];
})();

const config = {
  lazyCodeLoading: "requiredComponents", // https://developers.weixin.qq.com/miniprogram/dev/framework/ability/lazyload.html#%E7%94%A8%E6%97%B6%E6%B3%A8%E5%85%A5
  entryPagePath,
  tabBar: {
    custom: true,
    color: "#fff",
    selectedColor: "#fff",
    backgroundColor: "#fff",
    list: [
      {
        pagePath: "pages/index/index",
        text: "找工作"
      },
      {
        pagePath: "pages/resume/index",
        text: "找牛人"
      },
      {
        pagePath: "pages/msg-page/index",
        text: "消息"
      },
      {
        pagePath: "pages/ucenter/index",
        text: "会员中心"
      }
    ]
  },
  pages: [
    /** 首页 */
    "pages/index/index",
    /** 找活列表 */
    "pages/resume/index",
    /** 会员中心 */
    "pages/ucenter/index",
    "pages/msg-page/index",
    /** 务工保团险支付页 */
    "pages/page/wxAttestation/wxAttestation",
    ...swanPages
  ],
  requiredPrivateInfos,
  subPackages: [
    /** 百度招工seo */
    ...(ENV_IS_SWAN ? swanrecruitseo : []),
    {
      /** 全局的通用工具lib */
      root: "commonPackage/general",
      pages: ["index/index"]
    },
    {
      /** lib */
      root: "commonPackage/async-validator",
      pages: ["index/index"]
    },
    {
      root: "subpackage/msg-page",
      pages: ["index"]
    },
    {
      /** lib */
      root: "commonPackage/crypto",
      pages: ["index/index"]
    },
    {
      /** lib */
      root: "commonPackage/tim",
      pages: ["index/index"]
    },
    {
      /** lib */
      root: "commonPackage/painter",
      pages: ["index/index"]
    },
    {
      root: "commonPackage/sdk-mp",
      pages: ["index/index"]
    },
    {
      /** 微信插件 */
      root: "commonPackage/wx-plugins",
      pages: ["index/index"],
      plugins: {
        // 极验-设备验
        guard: {
          version: "1.1.0",
          provider: "wx47cb3e96a69c25ef"
        }
      }
    },
    {
      /** lib */
      root: "subpackage/components",
      pages: ["index/index"]
      // independent: true // 公共分包可以设置为独立分包
    },
    {
      /** 人脸认证页面 */
      root: "subpackage/mp-ecard-sdk",
      pages: [
        "index/index",
        "protocol/service/index",
        "protocol/privacy/index",
        "protocol/userAccredit/index",
        "protocol/eid/index"
      ]
    },
    ...(() => {
      // 打包就不打包测试页面
      if (ENV_DEVELOPMENT === "PRO") {
        return [];
      }
      return [
        /** 用于测试的页面 */
        {
          root: "subpackage/test-page",
          pages: [
            "class/index",
            "index/index",
            "upload_test/index",
            "environment/index",
            "click/index",
            "feedback/index"
          ]
        }
      ];
    })(),
    /** 系统升级提示 页面 */
    {
      root: "subpackage/update-prompt",
      pages: ["index"]
    },
    /** web-view 页面 */
    {
      root: "subpackage/web-view",
      pages: ["index"]
    },
    /** 主站招工找活搜索页 */
    {
      root: "subpackage/search",
      pages: ["index"]
    },
    /** 招工详情 */
    {
      root: "subpackage/recruit",
      pages: [
        /** 招工详情 */
        "details/index",
        /** 我的招工详情 */
        "my_detail/index",
        /** 鱼泡快招小程序的快速发布招工页 */
        "quick_trick/index/index",
        /** 快速发布招工 */
        "fast_issue/index/index",

        "fast_issue/follow_wechat/index",
        /** 我的招工列表 */
        "published/index",
        /** 完善招工 */
        "improve-recruitment/index",
        /** 完善招工-填写表单 */
        "improve-recruitment-info/index",
        /** 完善招工-填写表单-职位关键词 */
        "improve-recruitment-word/index",
        /** 完善招工-职位收费项 */
        "improve-recruitment-charges/index",
        /** 修改我的招工 */
        "jisu_issue/index",
        /** 谁看过我的招工 */
        "seeme/index",
        /** 招工搜索页 */
        "listSearchResultsPage/index",
        /** 发布招工流程2 */
        "fast_issue/fast_pulish_second/index",
        /** 我的待开放招工详情 */
        "my-wait-detail/index",
        /** 个人主页 */
        "individual/index",
        /** 编辑待发布信息 */
        "edit-draft/index",
        /** 招工列表结构化筛选筛选 */
        "struct-filter/index",
        /** 职位排序-可拖拽列表 */
        "job-drag-order/index",
        /** 首页-金刚区-活动配置的二级页面 */
        "activity-zone-page/index",
        /** 选择公司页面 */
        "customer-relationship/index"
      ]
    },
    /** 简历/找活详情 */
    {
      root: "subpackage/resume",
      pages: [
        /** 完善简历页 */
        "perfect/index",
        /** 完善职业优势页 */
        "perfect-job-v4/index",
        /** 找活详情 */
        "detail/index",
        /** 我的找活名片 */
        "publish/index",
        /** 找活，我的项目列表 */
        "project_list/index",
        /** 找活，别人的项目列表 */
        "project_show_list/index",
        /** 找活列表搜索结果页 */
        "listSearchResultsPage/index",
        /** 找活，添加/修改工作经历 */
        "work_add/index",
        /** 找活，添加/修改项目经历 */
        "project_add/index",
        /** 找活，添加/修改教育经历 */
        "edu_add/index",
        /** 找活，我的技能证书列表 */
        "certificate_list/index",
        /** 找活，别人的技能证书列表 */
        "certificate_show_list/index",
        /** 找活，添加技能证书 */
        "certificate_add/index",
        /** 发布找活 */
        "resume_publish/index",
        // 发布找活后的三个完善页
        "resume_publish/complete/one/index",
        "resume_publish/complete/two/index",
        /** 谁看过我的找活 */
        "seeme/index",
        /** 查看他的招工信息 */
        "viewlog/index",
        /** 附近适合你的工人 */
        "recommend/index",
        /** 找活连续刷新 */
        "continuous_refresh/index",
        /** 编辑和修改信息-多行文本 */
        "edit_textarea/index",
        /** 编辑和修改信息-单行文本 */
        "edit_input/index",
        /** 审核失败原因 */
        "err_check/index",
        /** 想找什么工作(求职列表) */
        "job_list/index",
        /** 简历资格证书选择页 */
        "choose-cates/index",
        /** 同步简历更新页 */
        "synchronize/update/index",
        /** 同步简历更新编辑页 */
        "synchronize/edit/index",
        /** 新牛人找活完善流程
         * @name assume_office 就职公司页
         * @name school 学校页
         * @name major 专业页
         * @name tallest_education 最高学历页
         * @name work_content 工作内容页
         * @name personal_advantage 个人优势页
         * @name first_work_time 工作年限页
         * @name work_period 工作时间页
         * @name study_time 就读时间页
         * @name add_head_photo 添加头像页
         */
        "new_worker_perfect/assume_office/index",
        "new_worker_perfect/school/index",
        "new_worker_perfect/major/index",
        "new_worker_perfect/tallest_education/index",
        "new_worker_perfect/work_content/index",
        "new_worker_perfect/personal_advantage/index",
        "new_worker_perfect/first_work_time/index",
        "new_worker_perfect/work_period/index",
        "new_worker_perfect/study_time/index",
        "new_worker_perfect/add_head_photo/index",
        /** 基础信息 */
        "new_worker_perfect/base_info/index",
        /** 最近一份工作 */
        "new_worker_perfect/recent_work/index",
        /** 求职期望 */
        "new_worker_perfect/job_expect/index",
        /** 期望薪资 */
        "new_worker_perfect/salary_expect/index"
      ]
    },
    /** 地址选择 */
    {
      root: "subpackage/map",
      pages: [
        /** 地址选择-携带map地图 */
        "history/index",
        /** 地址选择-携带map地图 */
        "addr-info/index",
        /** 地址选择-携带map地图 */
        "location/index",
        /** 地址选择-搜索页 */
        "area/index",
        /** 城市选择页-6.5.0增加 */
        "address/index",
        /** 地址管理 */
        "add-manage/index",
        /** 编辑/添加地址 */
        "add-edit/index",
        /** 选择商圈 */
        "add-select-sq/index",
        /** 城市选择-搜索页 */
        "addr-search/index"
      ]
    },
    /** 找活置顶 */
    {
      root: "subpackage/topset",
      pages: [
        /** 找活置顶 */
        "topmset/index",
        /** 招工置顶 */
        "rectopmset/index",
        /** 置顶页 */
        "topcity/index"
      ]
    },
    /** 用户授权 */
    {
      root: "subpackage/userauth",
      pages: ["auth/index", "tel/index"]
    },
    /** 系统消息 */
    {
      root: "subpackage/member",
      pages: [
        // 我的消息
        "system_info/index",
        /** 修改个人资料 */
        "info/index",
        /** 个人资料编辑页 */
        "edit_info/index",
        /** 修改手机号 */
        "phone/index",
        /** 修改名字 */
        "name/index",
        /** 正式积分界面 */
        "integral/officialPoints/index",
        /** 积分来源/消耗记录 */
        "integral/list/index",
        /** 我的联系记录 */
        "myContactHistory/index",
        /** 评价管理 */
        "evaluation/index",
        /** 评价页 & 评价完成页 */
        "invite-evalute/index",
        /** 任务中心 */
        // "taskcenter/index",
        /** 大转盘 */
        "turntable/index",
        /** 实名认证 */
        "realname/index",
        /** 企业认证 */
        "firmAuth/index",
        /** 企业法人认证 */
        "firmLegalAuth/index",
        /** 授权书下载 */
        "download_letter/index",
        /** 企业授权书认证 */
        "firmProxyAuth/index",
        /** 法人签名授权+法人身份证认证 */
        "firmIdentityAuth/index",
        /** 相机页面 */
        "camera/index",
        /** 获取积分 */
        "getintegral/index",
        /** 工友查询 */
        "check/index",
        /** 我的邀请记录 */
        "invitation_record/index",
        /** 任务中心查看规则 */
        "jumpWebView/index",
        /** 务工保障 */
        "insurance/index",
        /** webview打开小程序中间页 */
        "insurance/openMini/index",
        /** 人脸识别 */
        "face/index",
        /** 企业子账号管理 */
        "sub_account/index",
        /** 谁看过我（包含谁看过我的招工/找活）*/
        "who_see/index",
        /** 企业微信加群中间页 仅仅是鱼泡快招处理*/
        ...(ENV_SUB === "findjzgr" ? ["group/wecom_group/index"] : []),
        /** web端企业认证来源落地页 */
        ...(true ? ["webFirmAuth/index"] : []),
        /** 解绑企业 */
        "releaseCompany/index",
        /** 班组vip */
        "teamVip/index",
        /** 班组vip支付 */
        "teamVip/payment/index",
        /** 安全保障中心 */
        "safetySecurity/index",
        /** 短信链接引导企业微信加群 */
        "sms_group/index",
        /** 举报中心 */
        "report-platform/index",
        /** 系统设置 */
        "system/index",
        "system_per/index",
        "system_resume/index",
        /** 个性化推荐设置*/
        "per_recommendation/index",
        /** 我的卡券 */
        "card_voucher/index",
        /** 切换账号 */
        "change-account/index",
        /** 活动通知 */
        "activity-list/index",
        /** 订单中心 */
        "order_center/index",
        /** 订单列表:VIP开通记录 */
        "order_list_vip/index",
        /** 黑名单*/
        "black_lips/index",
        /** 隐私协议 */
        "privacy/index",
        /** 子账号申请-三选一方式页 / 子账号申请审核结果页 */
        "firmVariousVerify/index",
        /** 子账号- 招聘授权书页面 */
        "firmAuthorization/index",
        /** 子账号- 在职证明页面 */
        "firmCertificate/index",
        /** 子账号- 营业执照页面 */
        "firmLicense/index",
        /** 企业认证- 只上传 营业执照 -认证流程 */
        "firmCompanyLicense/index",
        /** 实名解绑换绑页面一 */
        "unbindRealname/unbind_one/index",
        /** 实名解绑换绑页面二 */
        "unbindRealname/unbind_two/index",
        /** 实名解绑换绑页面三 */
        "unbindRealname/unbind_three/index",
        /** 强制实名页面 */
        "unbindRealname/mandatory_certification/index",
        /** C端-收藏职位&收藏老板-页面 */
        "c_collect/index",
        /** B端-收藏牛人-页面 */
        "b_collect/index",
        /** 鱼泡直聘公众号 */
        "ypzp-public-account/index",
        /** 企业管理 */
        "corporate-governance/index"
      ]
    },
    // 分享活动集合-专区
    {
      root: "subpackage/share-activity",
      pages: [
        /** 邀请人视角-邀请瓜分红包页面 */
        "inviter-red-envelope/index",
        /** 被邀请人视角-邀请瓜分红包页面 */
        "receive-red-envelope/index",
        /** 邀请瓜分红包页面-提现明细页面 */
        "withdrawal/index"
      ]
    },
    // 积分充值
    {
      root: "subpackage/recharge",
      pages: ["recharge/index"]
    },
    {
      root: "subpackage/other",
      pages: [
        /** 浏览器_下载APP页面 */
        "download/browser/index",
        /** 跳转记工记账中间页 */
        "invaid/index",
        /** 新手指南 */
        "course/index",
        /** 举报电话 */
        "report/index",
        /** 防骗指南 */
        "guide/index",
        /** 关于鱼泡 */
        "notice/index",
        /** 鱼泡资讯页面 */
        "yp_news/index",
        /** 鱼泡资讯-详情页面 */
        "news_details/index",
        /** ios退款落地页 */
        "refund-ios/index"
      ]
    },
    {
      root: "subpackage/subscribe",
      pages: [
        // 订阅 -控制中心
        "work/index",
        // 订阅 - 列表
        "list/index",
        // 订阅 - 消息
        "notice/index"
      ]
    },
    {
      root: "subpackage/video",
      pages: [
        // 拍摄视频简历
        "video_shoot/index",
        // 视频上传
        "video_upload/index",
        // 查看&修改我的求职视频
        "video_me/index",
        // 查看求职视频
        "video_others/index"
      ]
    },
    /** 今日推荐工人落地页 */
    {
      root: "subpackage/today",
      pages: ["index"]
    },
    {
      root: "subpackage/common",
      pages: [
        // 切换角色
        "switch-roles/index",
        // 投诉分类
        "complaint_classify/index",
        // 招工找活投诉页面
        "complaint/index",
        // 水印相机
        "watermark_camera/index"
      ]
    },
    /**
     * 系统设置中的业务
     * index: 账号异常 弹窗 系统提示页
     * firm_shield: 公司屏蔽相关
     */
    {
      root: "subpackage/systips",
      pages: [
        "index",
        "firm_shield/index/index",
        "firm_shield/explain/index",
        "firm_shield/advice/index"
      ]
    },
    // im
    {
      root: "subpackage/tim",
      pages: [
        // 会话页面
        "groupConversation/index",
        //常用语添加或修改页面
        "comWordsOper/index",
        //查看别人的资料
        "otherInfo/index",
        // im 举报
        "report/index",
        // 设置备注页面
        "setRemark/index",
        // 常用语管理页面
        "comWordsMan/index"
      ]
    },
    // 中间号
    {
      root: "subpackage/midphone",
      pages: [
        // 中间号介绍页
        "guide/index"
      ]
    },
    // 加群｜关注
    {
      root: "subpackage/attention",
      pages: [
        //加群
        "addgroup/index",
        // 关注公众号
        "public/index",
        // 企业微信加群
        "firm-addgroup/index"
      ]
    },
    /** 工种筛选器 */
    {
      root: "subpackage/classify",
      pages: ["bottom-full-screen/index"]
    },
    /**  找活筛选页面 */
    {
      root: "subpackage/tools-page/screen-picker",
      pages: ["index"]
    },
    /** 看视频赚积分 */
    {
      root: "subpackage/tools-page/video-integral",
      pages: ["index"]
    },
    /** 系统维护页面(服务宕机) */
    {
      root: "subpackage/tools-page/system-maintenance",
      pages: ["index"]
    },
    /** 招呼语管理页面 */
    {
      root: "subpackage/tools-page/greetingsMan",
      pages: ["index"]
    },
    /** 招呼语添加、修改页面 */
    {
      root: "subpackage/tools-page/greetingsOper",
      pages: ["index"]
    },
    /** 电话助手页面 */
    {
      root: "subpackage/tools-page/tel-assistant",
      pages: ["index"]
    },
    /** 接收邮箱编辑页 */
    {
      root: "subpackage/tools-page/email-oper",
      pages: ["index"]
    },
    /** 接收邮箱管理页 */
    {
      root: "subpackage/tools-page/email-man",
      pages: ["index"]
    },
    /** 批量追聊页面 */
    {
      root: "subpackage/tools-page/batch-follow-chat",
      pages: ["index"]
    },
    /** 批量追聊列表页面 */
    {
      root: "subpackage/tools-page/batch-follow-chat-list",
      pages: ["index"]
    },
    /** B端不合适自动回复语页面U */
    {
      root: "subpackage/tools-page/set-not-reply-lang",
      pages: ["index"]
    },
    /** B端不合适自动回复语添加、修改页面 */
    {
      root: "subpackage/tools-page/set-zdy-reply-lang",
      pages: ["index"]
    },
    /** B端不合适自动回复语添加、修改页面 */
    {
      root: "subpackage/tools-page/rel-job-oper",
      pages: ["index"]
    },
    /** 不合适/不感兴趣列表 */
    {
      root: "subpackage/tools-page/dislike-conversation-list",
      pages: ["index"]
    },

    // 职位打招呼语设置页面
    {
      root: "subpackage/live",
      pages: ["live_preview/index"]
    },
    /** 附件简历 */
    {
      root: "subpackage/attachment-resume",
      pages: [
        "upload-way/index",
        "upload-pc/index",
        "edit-name/index",
        "manages/index",
        "upload/index",
        "upload-sync/index",
        "send-to-email/index",
      ]
    },
    /** 企业主页 */
    {
      root: "subpackage/company",
      pages: [
        /** 企业主页 */
        "home/index",
        /** 企业主页编辑入口 */
        "edit/index",
        /** 公司发展阶段编辑 */
        "edit-finance/index",
        /** 公司人员规模编辑 */
        "edit-scale/index",
        /** 添加公司介绍 */
        "edit-introduce/index",
        /** 编辑公司LOGO */
        "edit-logo/index",
        /** 编辑公司标准工作时间 */
        "edit-work-time/index",
        /** 编辑产品 */
        "edit-product/index",
        /** 编辑福利 */
        "edit-bonus/index",
        /** 添加产品 */
        "add-product/index",
        /** 编辑人才发展 */
        "edit-talent/index",
        /** 企业视频预览 */
        "video-preview/index",
        /** 单行编辑企业信息通用页面 */
        "edit-info/index",
        /** 企业视频上传 */
        "video-upload/index",
        /** 公司相册 */
        "company-photo/index",
        /** 添加公司照片页面 */
        "company-add-img-page/index",
        /** 公司视频页面 */
        "company-video/index",
        /** 公司范例照片页面 */
        "company-standard/index",
        /** 多行编辑企业信息通用页面 */
        "edit-textarea/index"
      ]
    },
    /** 补充企业认证资料 */
    {
      root: "subpackage/certification",
      pages: [
        /** 补充验证 */
        "replenish/index",
        /** 提交验证 */
        "submit/index"
      ]
    },
    // 面试邀约
    {
      root: "subpackage/invite-interview",
      pages: [
        "interview-info-edit-b/index",
        "interview-info-b/index",
        "interview-info-c/index",
        "interview-schedule-b/index"
      ]
    },
    {
      root: "lazy", // 异步懒加载代码
      pages: ['page/index'],
    },
    // 账号注销
    {
      root: "subpackage/account",
      pages: [
        "log-off-code/index",
        "log-off-reason/index",
        "log-off-suc/index"
      ]
    },
  ],
  preloadRule: {
    "pages/index/index": {
      network: "all",
      packages: [
        "subpackage/common",
        "subpackage/recruit",
        "subpackage/classify",
        "subpackage/search",
        "subpackage/msg-page"
      ]
    },
    "pages/resume/index": {
      network: "all",
      packages: ["subpackage/resume"]
    },
    "subpackage/share-activity/receive-red-envelope/index": {
      network: "all",
      packages: ["subpackage/resume"]
    },
    "subpackage/recruit/details/index": {
      network: "all",
      packages: ["commonPackage/painter"]
    }
  },
  // workers: 'workers',
  window: {
    initialRenderingCache: "static",
    backgroundColor: "#f5f6fa",
    backgroundTextStyle: "light",
    // navigationBarBackgroundColor: "#0099ff",
    navigationBarTitleText: "",
    navigationBarTextStyle: "black",
    ...wid
  },
  style: "v2",
  usingComponents,
  componentPlaceholder: {
    "c-m": "view"
  },
  sitemapLocation: "sitemap.json",
  permission,
  ...sitemapSett,
  __usePrivacyCheck__: true,
  plugins: {},
  componentFramework: "glass-easel"
};

// 鱼泡网seo链接对应萌瓜建筑招工链接
if (ENV_IS_SWAN) {
  config.routes = [
    // 授权页面
    { path: "pages/userauth/index", page: "subpackage/userauth/auth/index" },
    // 充值页面
    {
      path: "pages/recharge/index",
      page: "subpackage/recharge/recharge/index"
    },
    // 我的找活名片
    {
      path: "pages/resume/publish/index",
      page: "subpackage/resume/publish/index"
    },
    // 编辑找活名片 n
    {
      path: "pages/resume/add_info/index",
      page: "subpackage/resume/publish/index"
    },
    // 新增人员信息 n
    { path: "pages/resume/add_member/index", page: "pages/index/index" },
    // 发布招工地图 n
    { path: "pages/map/recruit/index", page: "pages/index/index" },
    // 发布找活地图 n
    { path: "pages/map/resume/index", page: "pages/index/index" },
    // 用户实名选择地址 n
    { path: "pages/map/realname/index", page: "pages/index/index" },
    // 新增技能证书
    {
      path: "pages/resume/add_skill/index",
      page: "subpackage/resume/certificate_add/index"
    },
    // 新增项目经验
    {
      path: "pages/resume/add_project/index",
      page: "subpackage/resume/project_add/index"
    },
    // 项目经验列表
    {
      path: "pages/resume/projects/index",
      page: "subpackage/resume/project_list/index"
    },
    // 技能证书列表
    {
      path: "pages/resume/skills/index",
      page: "subpackage/resume/certificate_list/index"
    },
    // 找活详情
    {
      path: "pages/resume/preview/index",
      page: "subpackage/resume/detail/index"
    },
    // 用户登录
    { path: "pages/login/index", page: "subpackage/userauth/auth/index" },
    // 工地急招 n
    {
      path: "pages/business/index",
      page: "subpackage/recruit/fast_issue/index/index"
    },
    // 急速发布招工 n
    {
      path: "pages/recruit/jisu_issue/index",
      page: "subpackage/recruit/fast_issue/index/index"
    },
    // 发布招工
    {
      path: "pages/recruit/fast_issue/issue/index",
      page: "subpackage/recruit/fast_issue/index/index"
    },
    // 发布招工输入验证码 n
    {
      path: "pages/recruit/fast_issue/code/index",
      page: "subpackage/recruit/fast_issue/index/index"
    },
    // 完善招工 n
    {
      path: "pages/recruit/fast_issue/release/index",
      page: "subpackage/recruit/fast_issue/index/index"
    },
    // 完成发布 n
    { path: "pages/recruit/tips/index", page: "pages/index/index" },
    // 搜索页面
    { path: "subpackage/pages/search/index", page: "subpackage/search/index" },
    // 工友查询
    {
      path: "subpackage/pages/checkauth/index",
      page: "subpackage/member/check/index"
    },
    // 举报骗子
    {
      path: "subpackage/pages/report/index",
      page: "subpackage/other/report/index"
    },
    // 下载app n
    { path: "subpackage/pages/download/index", page: "pages/index/index" },
    // 排名规则 n
    { path: "subpackage/pages/ranking/index", page: "pages/index/index" },
    // 使用教程
    {
      path: "subpackage/pages/course/index",
      page: "subpackage/other/course/index"
    },
    // 更多项目经验 n
    { path: "subpackage/pages/projects/index", page: "pages/index/index" },
    // 关于鱼泡
    {
      path: "subpackage/pages/news/index",
      page: "subpackage/other/notice/index"
    },
    // 我的消息
    {
      path: "subpackage/pages/information/mymessage/index",
      page: "subpackage/member/system_info/index"
    },
    // 我的消息 n
    {
      path: "subpackage/pages/information/system/index",
      page: "subpackage/member/system_info/index"
    },
    // 大转盘
    { path: "subpackage/pages/turntable/index", page: "pages/index/index" },
    // 百度新招工推荐
    {
      path: "subpackage/pages/recommend/baidu-recruit/index",
      page: "pages/index/index"
    },
    // 招工推荐 n
    {
      path: "subpackage/pages/recommend/recruit/index",
      page: "pages/index/index"
    },
    // 找活推荐 n
    {
      path: "subpackage/pages/recommend/resume/index",
      page: "pages/resume/index"
    },
    // 反馈意见 n
    { path: "subpackage/pages/newfeedback/index", page: "pages/index/index" },
    // 反馈详情
    {
      path: "subpackage/pages/feedbackListDetails/index",
      page: "pages/index/index"
    },
    // 谁看过我的招工
    {
      path: "subpackage/pages/viewlog/recList/index",
      page: "subpackage/recruit/seeme/index"
    },
    // xxx的招工信息
    {
      path: "subpackage/pages/viewlog/res/index",
      page: "subpackage/resume/viewlog/index"
    },
    // 收藏列表
    {
      path: "subpackage/pages/collection/index",
      page: "subpackage/member/c_collect/index"
    },
    // 修改用户信息 n
    {
      path: "subpackage/pages/userinfo/info/index",
      page: "pages/ucenter/index"
    },
    // 用户完善信息 n
    {
      path: "subpackage/pages/userinfo/add/index",
      page: "pages/ucenter/index"
    },
    // 修改手机号 n
    {
      path: "subpackage/pages/userinfo/phone/index",
      page: "pages/ucenter/index"
    },
    // 修改秘密 n
    {
      path: "subpackage/pages/userinfo/updatePass/index",
      page: "pages/ucenter/index"
    },
    // 设置密码 n
    {
      path: "subpackage/pages/userinfo/pass/index",
      page: "pages/ucenter/index"
    },
    // 招工置顶 n
    {
      path: "subpackage/pages/newtopping/recRang/index",
      page: "pages/index/index"
    },
    // 招工置顶选择城市 n
    {
      path: "subpackage/pages/newtopping/recGion/index",
      page: "pages/index/index"
    },
    // 找活置顶 n
    {
      path: "subpackage/pages/newtopping/resRang/index",
      page: "pages/index/index"
    },
    // 找活置顶选择城市 n
    {
      path: "subpackage/pages/newtopping/resGion/index",
      page: "pages/index/index"
    },
    // 投诉 n
    { path: "subpackage/pages/newcomplaint/index", page: "pages/index/index" },
    // 积分来源
    {
      path: "subpackage/pages/integral/source/index",
      page: "subpackage/member/integral/list/index"
    },
    // 正式积分
    {
      path: "subpackage/pages/integral/official/index",
      page: "subpackage/member/integral/officialPoints/index"
    },
    // 积分消耗 m
    {
      path: "subpackage/pages/integral/expend/index",
      page: "subpackage/member/integral/list/index"
    },
    // 实名认证
    {
      path: "subpackage/pages/realname/index",
      page: "subpackage/member/realname/index"
    },
    // 获取积分
    {
      path: "subpackage/pages/getintegral/index",
      page: "subpackage/member/getintegral/index"
    },
    // 用户首次发布招工成功后的营销页
    {
      path: "subpackage/pages/marketing_page/index",
      page: "pages/index/index"
    },
    // 新版用户下载
    { path: "subpackage/pages/new_downApp/index", page: "pages/index/index" },
    // 找活助手
    {
      path: "subpackage/pages/lookForAssIstantInfo/index",
      page: "pages/index/index"
    },
    // 找活助手信息推送页面
    {
      path: "subpackage/pages/lookForAssIstantInfo/Information/index",
      page: "pages/index/index"
    },
    // 首页跳转招工列表新页面 n
    { path: "subpackage/pages/recruit_list/index", page: "pages/index/index" },
    // 会员中心
    { path: "pages/member/ucenter/index", page: "pages/ucenter/index" },
    // 快速发布招工
    {
      path: "pages/recruit/fast_issue/index/index",
      page: "subpackage/recruit/fast_issue/index/index"
    },
    // 招工详情
    {
      path: "pages/detail/info/index",
      page: "subpackage/recruit/details/index"
    },
    // 待开放-我的招工详情页面
    {
      path: "pages/detail/my-wait-detail/index",
      page: "subpackage/recruit/my-wait-detail/index"
    },
    // 找活详情页面
    {
      path: "pages/resume/detail/index",
      page: "subpackage/resume/detail/index"
    },

    // 鱼泡咨询详情
    {
      path: "subpackage/pages/anti-fraud/index",
      page: "subpackage/other/guide/index"
    },
    // 搜索页面
    { path: "subpackage/common/search/index", page: "subpackage/search/index" },
    // 找牛人页面
    { path: "pages/resume/lists/index", page: "pages/resume/index" },
    // 我的招工信息
    {
      path: "subpackage/pages/published/recruit/index",
      page: "subpackage/recruit/published/index"
    },
    //积分来源
    {
      path: "subpackage/pages/integral/tabber/index",
      page: "subpackage/member/integral/list/index"
    },
    //谁看过我的找活
    {
      path: "subpackage/pages/viewlog/resList/index",
      page: "subpackage/resume/seeme/index"
    },
    //更多技能证书
    { path: "subpackage/pages/skills/index", page: "pages/index/index" },
    //关于我们
    {
      path: "subpackage/pages/about/index",
      page: "subpackage/other/notice/index"
    },
    //预览
    { path: "pages/resume/newPreview/index", page: "pages/index/index" },
    //资讯详情
    {
      path: "pages/static/notice/index"
    },
    { path: "pages/home/<USER>", page: "pages/index/index" },
    //今日推荐工人落地页
    { path: "subpackage/today/index", page: "subpackage/today/index" },
    //发布招工流程2
    {
      path: "subpackage/recruit/fast_issue/fast_pulish_second/index",
      page: "subpackage/recruit/fast_issue/fast_pulish_second/index"
    }
  ];
}

//5.4.0-加群优化添加
if (ENV_SUB === "findjzgr") {
  config.plugins = {
    ...config.plugins,
    materialPlugin: {
      version: "1.0.12",
      provider: "wx4d2deeab3aed6e5a"
    }
  };
}
export default config;
