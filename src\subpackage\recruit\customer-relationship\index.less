/* 发布招工-选择公司页面样式 */
.customer-relationship-page {
  min-height: 100vh;
  background-color: #f5f5f5;

  .page-content {
    padding: 0 32rpx;
    padding-bottom: 200rpx; // 为底部按钮留出空间

    .page-desc {
      padding: 32rpx 0;
      background-color: #fff;
      margin: 24rpx -32rpx 24rpx -32rpx;
      padding-left: 32rpx;
      padding-right: 32rpx;

      .desc-text {
        font-size: 28rpx;
        line-height: 40rpx;
        color: #666;
      }
    }

    .company-list-container {
      .company-scroll-view {
        height: calc(100vh - 300rpx);
        background-color: #fff;
        border-radius: 16rpx;
        overflow: hidden;

        .company-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 32rpx;
          border-bottom: 1rpx solid #f0f0f0;
          background-color: #fff;
          transition: background-color 0.2s;

          &:last-child {
            border-bottom: none;
          }

          &:active {
            background-color: #f8f8f8;
          }

          .company-info {
            flex: 1;
            margin-right: 24rpx;

            .company-name {
              font-size: 32rpx;
              font-weight: 500;
              color: #333;
              line-height: 44rpx;
              display: block;
              margin-bottom: 12rpx;
            }

            .company-tags {
              display: flex;
              gap: 12rpx;

              .tag {
                padding: 4rpx 12rpx;
                border-radius: 8rpx;
                font-size: 24rpx;
                line-height: 32rpx;

                &.agent-tag {
                  background-color: #e6f7ff;
                  color: #0099ff;
                }

                &.outsource-tag {
                  background-color: #f0f9ff;
                  color: #1890ff;
                }
              }
            }
          }
        }

        .loading-more {
          padding: 32rpx;
          text-align: center;

          .loading-text {
            font-size: 28rpx;
            color: #999;
          }
        }

        .no-more {
          padding: 32rpx;
          text-align: center;

          .no-more-text {
            font-size: 28rpx;
            color: #999;
          }
        }
      }
    }

    .empty-container {
      margin-top: 120rpx;

      .custom-empty {
        .add-company-btn {
          margin-top: 48rpx;
          width: 200rpx;
          height: 80rpx;
          border-radius: 40rpx;
          font-size: 28rpx;
        }
      }
    }

    .bottom-btn-container {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 24rpx 32rpx;
      padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
      padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
      background-color: #fff;
      border-top: 1rpx solid #f0f0f0;

      .bottom-add-btn {
        width: 100%;
        height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}

