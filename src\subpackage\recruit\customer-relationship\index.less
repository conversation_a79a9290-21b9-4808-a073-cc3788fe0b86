/* 发布招工-选择公司页面样式 */
.customer-relationship-page {
  min-height: 100vh;
  background-color: #f5f5f5;

  .page-content {
    padding: 0 32rpx;
    padding-bottom: 120rpx; // 为底部按钮留出空间

    .page-title {
      padding: 32rpx 0 24rpx 0;

      .title-text {
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        line-height: 56rpx;
      }
    }

    .page-desc {
      padding-bottom: 32rpx;

      .desc-text {
        font-size: 28rpx;
        line-height: 40rpx;
        color: #666;
      }
    }

    .company-list-container {
      .company-scroll-view {
        height: calc(100vh - 300rpx);
        background-color: #fff;
        border-radius: 16rpx;
        overflow: hidden;

        .company-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 32rpx;
          border-bottom: 1rpx solid #f0f0f0;
          background-color: #fff;
          transition: background-color 0.2s;

          &:last-child {
            border-bottom: none;
          }

          &:active {
            background-color: #f8f8f8;
          }

          .company-info {
            flex: 1;
            margin-right: 24rpx;

            .company-content {
              .company-name-container {
                display: flex;
                align-items: flex-start;
                line-height: 44rpx;
                max-height: 88rpx; // 两行的高度
                overflow: hidden;

                .company-name {
                  font-size: 32rpx;
                  font-weight: 500;
                  color: #333;
                  line-height: 44rpx;
                  word-break: break-all;
                  flex: 1;
                  min-width: 0;
                }

                .company-tags {
                  display: flex;
                  gap: 8rpx;
                  align-items: flex-start;
                  flex-shrink: 0;
                  margin-left: 8rpx;
                  margin-top: 6rpx; // 微调对齐

                  .tag {
                    display: inline-block;
                    padding: 4rpx 12rpx;
                    border-radius: 8rpx;
                    font-size: 24rpx;
                    line-height: 32rpx;
                    white-space: nowrap;

                    &.agent-tag {
                      background-color: #e6f7ff;
                      color: #0099ff;
                    }

                    &.outsource-tag {
                      background-color: #f0f9ff;
                      color: #1890ff;
                    }
                  }
                }
              }
            }
          }
        }

        .loading-more {
          padding: 32rpx;
          text-align: center;

          .loading-text {
            font-size: 28rpx;
            color: #999;
          }
        }

        .no-more {
          padding: 32rpx;
          text-align: center;

          .no-more-text {
            font-size: 28rpx;
            color: #999;
          }
        }
      }
    }

    .empty-container {
      margin-top: 120rpx;

      .custom-empty {
        .add-company-btn {
          margin-top: 48rpx;
          width: 200rpx;
          height: 80rpx;
          border-radius: 40rpx;
          font-size: 28rpx;
        }
      }
    }


  }
}

