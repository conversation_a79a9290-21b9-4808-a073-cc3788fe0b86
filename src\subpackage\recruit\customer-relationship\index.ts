/*
 * @Description: 发布招工-选择公司页面
 */
import { app, share } from '@/config/index'
import { helper, tools } from '@/utils/index'
import { MapStateToData, connectPage, dispatch, actions, store } from '@/store/index'

import { getShareInfo, getSharePathInfo, setSharePath } from '@/utils/helper/share/index'
import { MOBILE_TERMINAL_NEW_URL } from '@/config/app'
import { toLogin } from '@/utils/helper/common/toLogin'

/** 初始化状态管理数据 */
const mapStateToData: MapStateToData = (state) => {
  return {
  }
}

Page(
  connectPage(mapStateToData)({
    data: {
      isIOS: tools.validator.isIos(),
      companyList: [], // 公司列表
      loading: false, // 加载状态
      hasMore: true, // 是否还有更多数据
      page: 1, // 当前页码
      pageSize: 20, // 每页数量
      selectedCompany: null, // 选中的公司
    },

    /** 生命周期函数--监听页面加载 */
    onLoad() {
      this.loadCompanyList()
    },

    /** 生命周期函数--监听页面显示 */
    onShow() {
      // 页面显示时可以刷新数据
    },

    /** 页面相关事件处理函数--监听用户下拉动作 */
    onPullDownRefresh() {
      this.refreshCompanyList()
    },

    /** 页面上拉触底事件的处理函数 */
    onReachBottom() {
      this.onLoadMore()
    },

    /** 加载公司列表 */
    async loadCompanyList(isRefresh = false) {
      if (this.data.loading) return

      const page = isRefresh ? 1 : this.data.page

      this.setData({ loading: true })

      try {
        // 模拟API调用 - 实际项目中需要替换为真实的API
        const mockData = this.getMockCompanyData(page)

        const newList = isRefresh ? mockData.list : [...this.data.companyList, ...mockData.list]

        this.setData({
          companyList: newList,
          hasMore: mockData.hasMore,
          page: page + 1,
          loading: false,
        })

        if (isRefresh) {
          wx.stopPullDownRefresh()
        }
      } catch (error) {
        console.error('加载公司列表失败:', error)
        this.setData({ loading: false })
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none',
        })
      }
    },

    /** 刷新公司列表 */
    refreshCompanyList() {
      this.setData({ page: 1, hasMore: true })
      this.loadCompanyList(true)
    },

    /** 加载更多 */
    onLoadMore() {
      if (this.data.hasMore && !this.data.loading) {
        this.loadCompanyList()
      }
    },

    /** 选择公司 */
    onSelectCompany(e: any) {
      const { item } = e.currentTarget.dataset

      // 更新选中状态
      const updatedList = this.data.companyList.map((company: any) => ({
        ...company,
        selected: company.id === item.id,
      }))

      this.setData({
        companyList: updatedList,
        selectedCompany: item,
      })

      // 可以在这里添加选中后的逻辑，比如返回上一页并传递选中的公司信息
      wx.showToast({
        title: `已选择：${item.name}`,
        icon: 'success',
      })

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack({
          success: () => {
            // 可以通过事件总线或其他方式传递选中的公司信息
            const pages = getCurrentPages()
            const prevPage = pages[pages.length - 2]
            if (prevPage && prevPage.onCompanySelected) {
              prevPage.onCompanySelected(item)
            }
          },
        })
      }, 1000)
    },

    /** 新增公司 */
    onAddCompany() {
      // 跳转到新增公司页面
      wx.navigateTo({
        url: '/subpackage/company/add/index', // 需要根据实际路径调整
      })
    },

    /** 获取模拟数据 */
    getMockCompanyData(page: number) {
      // 模拟数据 - 实际项目中需要替换为真实的API调用
      const mockCompanies = [
        { id: 1, name: '北京抖音信息有限公司', isAgent: true, isOutsource: false },
        { id: 2, name: '北京抖音信息有限公司', isAgent: true, isOutsource: true },
        { id: 3, name: '北京抖音信息有限公司北京抖音信息有限公司北京抖音信息有限公司', isAgent: false, isOutsource: true },
        { id: 4, name: '北京抖音信息有限公司北京抖音信息有限公司北京抖音信息有限公司', isAgent: false, isOutsource: true },
        { id: 5, name: '上海腾讯科技有限公司', isAgent: false, isOutsource: false },
        { id: 6, name: '深圳华为技术有限公司', isAgent: true, isOutsource: false },
        { id: 7, name: '杭州阿里巴巴网络技术有限公司', isAgent: false, isOutsource: true },
        { id: 8, name: '北京百度网讯科技有限公司', isAgent: true, isOutsource: true },
        { id: 9, name: '广州网易计算机系统有限公司', isAgent: false, isOutsource: false },
        { id: 10, name: '成都字节跳动科技有限公司', isAgent: true, isOutsource: false },
      ]

      // 模拟分页 - 如果页面为1且没有数据，返回空数组来测试空状态
      if (page === 1 && mockCompanies.length === 0) {
        return {
          list: [],
          hasMore: false,
        }
      }

      const startIndex = (page - 1) * this.data.pageSize
      const endIndex = startIndex + this.data.pageSize
      const list = mockCompanies.slice(startIndex, endIndex)

      return {
        list: list.map(item => ({ ...item, selected: false })),
        hasMore: endIndex < mockCompanies.length,
      }
    },
  }),
)
