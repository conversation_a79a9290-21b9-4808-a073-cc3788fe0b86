/*
 * @Description: 发布招工-添加公司页面
 */
import { app, share } from '@/config/index'
import { helper, tools } from '@/utils/index'
import { MapStateToData, connectPage, dispatch, actions, store } from '@/store/index'

import { getShareInfo, getSharePathInfo, setSharePath } from '@/utils/helper/share/index'
import { MOBILE_TERMINAL_NEW_URL } from '@/config/app'
import { toLogin } from '@/utils/helper/common/toLogin'

/** 初始化状态管理数据 */
const mapStateToData: MapStateToData = (state) => {
  return {
  }
}
Page(
  connectPage(mapStateToData)({
    data: {
      isIOS: tools.validator.isIos(),
    },
  }),
)
