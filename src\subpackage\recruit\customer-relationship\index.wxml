<view class="customer-relationship-page">
  <!-- 导航栏 -->
  <custom-header title="选择公司" border custom-style="background-color:#fff;" />

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 页面描述 -->
    <view class="page-desc">
      <text class="desc-text">请选择求职者实际工作的用工方，如果您的业务为连锁职位二手单业务，请选择此职位的直接用工方。</text>
    </view>

    <!-- 有数据时显示列表 -->
    <view wx:if="{{companyList.length > 0}}" class="company-list-container">
      <scroll-view
        scroll-y="{{true}}"
        class="company-scroll-view"
        bindscrolltolower="onLoadMore"
        lower-threshold="100"
      >
        <view wx:for="{{companyList}}" wx:key="id" class="company-item" data-item="{{item}}" bindtap="onSelectCompany">
          <view class="company-info">
            <text class="company-name">{{item.name}}</text>
            <view class="company-tags">
              <text wx:if="{{item.isAgent}}" class="tag agent-tag">代理</text>
              <text wx:if="{{item.isOutsource}}" class="tag outsource-tag">派遣&外包</text>
            </view>
          </view>
          <icon-font wx:if="{{item.selected}}" type="yp-icon_check" size="40rpx" color="#0099FF" />
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{loading}}" class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>
        <view wx:elif="{{!hasMore}}" class="no-more">
          <text class="no-more-text">- 没有更多内容了 -</text>
        </view>
      </scroll-view>
    </view>

    <!-- 无数据时显示空状态 -->
    <view wx:else class="empty-container">
      <empty-box
        img="list"
        title="暂未添加客户公司"
        custom-class="custom-empty"
      >
        <m-button
          type="primary"
          btnTxt="去新增"
          custom-class="add-company-btn"
          bindtap="onAddCompany"
        />
      </empty-box>
    </view>

    <!-- 底部按钮 -->
    <view wx:if="{{companyList.length > 0}}" class="bottom-btn-container">
      <m-button
        type="primary"
        btnTxt="新增客户公司"
        custom-class="bottom-add-btn"
        bindtap="onAddCompany"
      />
    </view>
  </view>
</view>