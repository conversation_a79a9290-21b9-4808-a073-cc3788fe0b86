.custom-header {
  position: sticky;
  top: 0;
  z-index: 99;
  width: 100vw;
}

.section {
  padding-bottom: calc(208rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(208rpx + env(safe-area-inset-bottom));
  background-color: transparent;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.resume-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.header-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 32rpx;
  font-size: 34rpx;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
}

.header-icon {
  margin-right: 44rpx;
}

.home-class {
  left: 85rpx !important;
}

.title {
  font-size: 34rpx;
  font-weight: bold;
  line-height: 48rpx;
  color: rgba(0, 0, 0, 0.85);
}

.content {
  display: flex;
  align-items: center;
  margin-top: 32rpx;
  width: 100%;
  position: relative;
}


/** 底部小横条高度 */
.stripes {
  width: 100%;
  background-color: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/** 积分充值弹窗 */

.footerW {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  background-color: #fff;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  background: rgba(255, 255, 255, 1);
}

.footer-cont {
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  margin-right: -20rpx;
  background-color: #fff;
  box-shadow: 0 0 10rpx #e7e6e4;
}

.footer-right {
  flex: 1;
  display: flex;

  .btn-icon {
    margin-right: 12rpx;
  }

  .btn-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 96rpx;
    line-height: 96rpx;
    border-radius: 12rpx;
    font-size: 34rpx;
    background: #0092ff;
    margin-right: 20rpx;
    color: #fff;
    flex: 1;
  }

  .chat-btn-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 96rpx;
    line-height: 96rpx;
    border-radius: 12rpx;
    font-size: 34rpx;
    background: #00cbff;
    margin-right: 20rpx;
    color: #fff;
    flex: 1;
  }

  .btn-item-cont {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    line-height: 88rpx;
  }

  .phone-icon {
    display: flex;
    padding-right: 8rpx;
  }

  .btn-text {
    font-weight: bold;
  }

  .btn-call-count {
    position: absolute;
    right: -60rpx;
    top: -22rpx;
    min-width: 42rpx;
    height: 38rpx;
    padding: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f74742;
    border: 4rpx solid white;
    border-radius: 36rpx;
    border-bottom-left-radius: 16rpx;

    .btn-call-count-text {
      font-size: 24rpx;
      display: flex;
      color: #fffefe;
      font-weight: bold;
    }
  }
}

/* 附近适合您的工人 */

.chat-btn {
  /* width: 220rpx;*/
  flex: 1;
  height: 96rpx;
  background: #00cbff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
}

.normalWeight {
  font-weight: normal !important;
}

.share-box {
  width: 98rpx !important;
  height: 98rpx !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  margin-right: 20rpx !important;
  padding: 0 !important;
  background-color: transparent !important;

  .txt {
    margin-top: 2rpx;
    color: rgba(0, 0, 0, 0.65);
    font-size: 26rpx;
    font-weight: 500 !important;
    white-space: nowrap !important;
  }
}

.slide-tip {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.55);
  transition: opacity 0.2s ease-in-out;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.slide-gesture {
  width: 388rpx;
  height: 188rpx;
}

.tip-text {
  color: #fff;
  font-size: 30rpx;
  line-height: 42rpx;
}

.empty {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.boxClass {
  position: relative;
  margin-bottom: 24rpx !important;
  z-index: 130;
}

.xz-banner {
  width: 100%;
  padding: 12rpx 32rpx;
  background: rgba(255, 239, 222, 1);
  color: rgba(255, 137, 4, 1);
  font-size: 26rpx;
}

.xz-btn-box {
  width: 100%;
  padding: 24rpx 32rpx;
}

.xz-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 16rpx;
  background-color: rgb(0, 146, 255);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.xz-btn-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
  font-size: 34rpx;
  font-weight: bold;
}

.header-out {
  justify-content: space-between !important;
  margin: 0 !important;
}

.inappropriate {
  padding: 24rpx 32rpx;
}

.ina-btn{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  width: 100%;
  border-radius: 16rpx;
  background: rgba(232, 54, 46, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
}