page {
  background-color: #fff;
}

.hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

.body {
  padding: 32rpx;
  padding-bottom: 0;
  position: relative;
}

.input-box {
  position: relative;
  display: flex;
  align-items: center;
  .bottom-line();
}

.input-right {
  display: flex;
  align-items: center;
  .input-clear{
    opacity: 0.7;
    padding: 6rpx 24rpx;
    display: inline-flex;
  }
  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: @primary-color;
  }
  .num-err {
    color: @error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.input {
  height: 112rpx !important;
  line-height: 112rpx !important;
}

.sensitive-input {
  display: block;
  flex: 1;
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
}

.search-list {
  position: relative;
  /* background-color: red;*/
  .item {
    padding: 40rpx 32rpx;
    position: relative;
    z-index: 9;
    .bottom-line();
    &::after {
      left: 32rpx;
      right: 32rpx;
    }
  }
  .search-back{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
}


.warning-tip {
  padding: 12rpx 32rpx !important;
}
