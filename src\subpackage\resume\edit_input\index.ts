/**
 * @description: 编辑单行文本框
 * @query: {
 *    type: ''
 *  }
 */

import { dealDialogShow } from '@/utils/helper/dialog/index'
import { getHeaderHeight, throttle } from '@/utils/tools/common/index'
import { getParams, getEduList, getWorkList, getEduMajorList, infoData, IType } from './utils'

Page(class extends wx.$.Page {
  data = {
    info: {},
    title: ' ',
    maxContent: 500,
    /** 输入框的值 */
    content: '',
    /** 旧的输入框的值 */
    contentOld: '',
    /** 顶部高度 */
    top: getHeaderHeight('120rpx', true),
    /** 是否获取了焦点 */
    isFocus: false,
    /** 键盘高度 */
    bottomHeight: 0,
    /** 类型 */
    type: 'introduce' as IType,
    /** 搜索列表 */
    searchList: [],
    /** 选择search的值: {} */
    selectSearch: {},
    /** 是否必填 */
    isRequire: false,
    /** 是否有敏感词 */
    isWarning: false,
    /** 值相同验证 */
    sameContentVerify: true,
  }

  onLoad(options) {
    const type = options.type || 'eduSchoolName'
    const sameContentVerify: boolean = options.sameContentVerify !== 'false'
    const info = infoData[type] || {}
    const { content, isRequire } = getParams()
    this.setData({
      info,
      type,
      title: info.title || ' ',
      content,
      contentOld: content,
      maxContent: info.maxContent || 100,
      bottomHeight: 0,
      isRequire,
      sameContentVerify,
    })
  }

  onHide() {
    this.sensitiveHideKey()
  }

  /** 点击提交按钮 */
  onSubmit() {
    const selectSearch = this.data.selectSearch || {}
    const content = `${this.data.content}`.trim()
    if (!this.saveBool()) {
      return
    }
    this.sensitiveHideKey()
    wx.$.nav.event({ ...selectSearch, content })
    wx.$.nav.back()
  }

  /** 判断输入的内容是否有效 */
  saveBool() {
    const { content, maxContent, contentOld, type, isRequire, sameContentVerify } = this.data

    const val = `${this.data.content || ''}`.trim()
    if (val == '') {
      if (type === 'proTitle' && isRequire) {
        wx.$.msg('请完善项目名称')
        return false
      }
      if (type === 'proRole' && isRequire) {
        wx.$.msg('请完善项目角色')
        return false
      }
    }
    if (sameContentVerify && content && content === contentOld) {
      wx.$.r.back()
      return false
    }
    if (content.length > maxContent) {
      wx.$.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  /** 点击返回按钮逻辑 */
  onNavBack() {
    this.sensitiveHideKey()
    const { content, contentOld } = this.data
    if (content === contentOld) {
      wx.$.r.back()
      return
    }
    if (!this.data.content || `${this.data.content}`.trim() == '') {
      wx.$.r.back()
      return
    }
    dealDialogShow({
      dialogIdentify: 'jlwbcfhts',
    }).then(res => {
      const { itemClass, btnIndex } = res || { btnIndex: 0, itemClass: 'cancel' }
      if (itemClass == 'none') {
        wx.$.confirm({
          content: '内容尚未保存,确定退出?',
        }).then(() => {
          wx.$.r.back()
        }).catch(() => {})
        return
      }
      if (btnIndex == 0) {
        return
      }
      wx.$.r.back()
    })
  }

  onFocus(e) {
    this.setData({
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    })
  }

  /** blur */
  onHideKey() {
    this.setData({ isFocus: false, bottomHeight: 0 })
  }

  /** 输入框input事件 */
  onInput(e) {
    const { value } = e.detail
    const selectSearch = this.data.selectSearch || {}
    value !== this.data.content && this.onSearch(value)
    const sData = { content: value } as any
    if (selectSearch.name !== value) {
      sData.selectSearch = {}
    }
    this.setData(sData)
  }

  /** 选择搜索列表的值 */
  onSelect(e) {
    const { item } = e.currentTarget.dataset
    this.handleInputUpdate(item.name)
    this.setData({
      selectSearch: item,
      searchList: [],
    })
    this.sensitiveHideKey()
  }

  /** 搜索数据 */
  onSearch = throttle(function (keywords) {
    const { type } = this.data
    if (type === 'workComName') { // 查询公司名称列表
      getWorkList(keywords).then((searchList) => {
        keywords === this.data.content && this.setData({ searchList })
      })
      return
    }
    if (type === 'eduSchoolName') { // 查询学校名称列表
      getEduList(keywords).then((searchList) => {
        keywords === this.data.content && this.setData({ searchList })
      })
      return
    }
    if (type === 'eduMajorName') { // 查询专业名称列表
      getEduMajorList(keywords).then((searchList) => {
        keywords === this.data.content && this.setData({ searchList })
      })
    }
  }, 300)

  onClear() {
    this.handleInputUpdate()
    this.setData({ searchList: [], isWarning: false })
  }

   /** 敏感词 */
  onKeyChange(e) {
    this.setData({
      isWarning: !!e.detail,
    })
  }
  
  /** 禁止滚动操作 */
  onCatch() { }

  /** 输入框更新数据 */
  handleInputUpdate(value = '') {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.updateValue()
      const sData = { content: value, isWarning: false } as any
      if (!value) {
        sData.selectSearch = {}
      }
      this.setData(sData)
    })
  }
  /** 收起敏感词输入框键盘 */
  sensitiveHideKey() {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.setData({inputFocus: false})
    })
  }
})
