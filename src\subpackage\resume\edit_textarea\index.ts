/**
 * @description: 编辑文本域
 * @query: {
 *  type: 'introduce'
 *    - introduce: 个人优势
 *    - workCont: 工作内容
 *    - workKPI: 工作业绩
 *    - project: 项目描述
 *    - schoolExp: 在校经历
 *  }
 */

import { dealDialogShow } from '@/utils/helper/dialog/index'
import { refreshMyInfo } from '@/utils/helper/resume/index'
import { editIntroduce, getContent, infoData, IType } from './utils'
import { updateResume } from '../utils/index'
import { getDom } from '@/utils/tools/common/index'
import { tools } from '@/utils/index'

Page(class extends wx.$.Page {
  data = {
    info: {},
    maxContent: 500,
    /** 输入框的值 */
    content: '',
    /** 旧的输入框的值 */
    contentOld: '',
    /** 是否获取了焦点 */
    isFocus: false,
    /** 键盘高度 */
    bottomHeight: 0,
    /** 类型 */
    type: 'introduce' as IType,
    /** 是否有敏感词 */
    isWarning: false,
    /** 敏感词高度 */
    warningHeight: 0,
    /** 顶部banner的高度 */
    headerTopHeight: '',
    /** 底部footer高度 */
    footerHeight: 0,
  }

  onLoad(options) {
    const type = options.type || 'introduce'
    const info = infoData[type] || {}
    const content = getContent(type)
    updateResume()
    this.setData({
      info,
      type,
      content,
      contentOld: content,
      maxContent: info.maxContent || 500,
      bottomHeight: 0,
    })
    this.getNodeHeight()
  }

  /** 点击提交按钮 */
  onSave() {
    if (!this.saveBool()) {
      return
    }
    this.sensitiveHideKey()
    // wx.$.msg('保存成功')
    // 提交数据
    this.submitRequest()
  }

  submitRequest() {
    const { type } = this.data
    const content = `${this.data.content}`.trim()
    if (type === 'introduce') {
      // 个人优势不能为空
      if (!content || content.trim() === '') {
        wx.$.msg('个人优势不能为空')
        return
      }
      editIntroduce.call(this, content).then((res) => {
        if (res.code === 0) {
          refreshMyInfo()
        }
      })
      return
    }
    wx.$.nav.event({ content })
    wx.$.nav.back()
  }

  /** 判断输入的内容是否有效 */
  saveBool() {
    const { content, maxContent, contentOld } = this.data
    if (content === contentOld) {
      wx.$.r.back()
      return false
    }
    if (content.length > maxContent) {
      wx.$.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  /** 点击返回按钮逻辑 */
  onNavBack() {
    this.sensitiveHideKey()
    const { content, contentOld } = this.data
    if (content === contentOld) {
      wx.$.r.back()
      return
    }
    if (!this.data.content || `${this.data.content}`.trim() == '') {
      wx.$.r.back()
      return
    }

    dealDialogShow({
      dialogIdentify: 'jlwbcfhts',
    }).then(res => {
      const { itemClass, btnIndex } = res || { btnIndex: 0, itemClass: 'cancel' }
      if (itemClass == 'none') {
        wx.$.confirm({
          content: '内容尚未保存,确定退出?',
        }).then(() => {
          wx.$.r.back()
        }).catch(() => {})
        return
      }
      if (btnIndex == 0) {
        return
      }
      wx.$.r.back()
    })
  }

  onFocus(e) {
    this.setData({
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    })
  }

  onLongTap(e) {
    if (!this.data.isFocus) {
      this.setData({
        isFocus: true,
        bottomHeight: e.detail.height || 0,
      })
    }
  }

  onHeightChange(e) {
    this.setData({
      bottomHeight: e.detail.height || 0,
    })
  }

  /** 收起键盘 */
  onHideKey(e) {
    console.log('onHideKey=====>', e, e?.type !== 'heightChange')
    if (e?.type !== 'heightChange') {
      return
    }
    const { warningHeight, isWarning } = this.data
    if (isWarning && !warningHeight) {
      this.getWarningHeight()
    }
    this.getNodeHeight()
    this.setData({ isFocus: false, bottomHeight: 0 })
  }

  onInput(e) {
    this.setData({ content: e.detail.value })
  }

  onClear() {
    this.handleInputUpdate()
  }

  /** 敏感词 */
  async onKeyChange(e) {
    this.setData({
      isWarning: !!e.detail,
    })
    e.detail && this.getWarningHeight()
  }

  /** 获取warning-tip高度 */
  async getWarningHeight() {
    if (!this.data.warningHeight) {
      const dom = await getDom.call(this, '#warning-tip')
      // 如果没拿到高度就再获取一次
      if (!dom?.height) {
        setTimeout(async () => {
          const dom = await getDom.call(this, '#warning-tip')
          this.setData({
            warningHeight: dom?.height || 0,
          })
        }, 100)
        return
      }
      this.setData({
        warningHeight: dom?.height || 0,
      })
    }
  }

  /** 通过输入框组件更新数据 */
  handleInputUpdate() {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.updateValue()
      this.setData({ content: '', isWarning: false })
    })
  }

  /** 收起敏感词输入框键盘 */
  sensitiveHideKey() {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.setData({ inputFocus: false })
    })
  }

  /** 获取顶部｜底部节点高度 */
  async getNodeHeight() {
    const { headerTopHeight, footerHeight } = this.data
    if (headerTopHeight && footerHeight) {
      return
    }
    const [header, footer] = await Promise.all([getDom('.head'), getDom('.footer')])
    this.setData({
      headerTopHeight: tools.common.getHeaderHeight(`40rpx + ${header?.height || 0}px`),
      footerHeight: footer?.height || 0,
    })
  }
})
