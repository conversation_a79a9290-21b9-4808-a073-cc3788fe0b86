/*
 * @Date: 2024-04-01 15:34:34
 * @Description: 求职期望列表页
 */

import { actions, dispatch, store } from '@/store/index'
import {
  getGlobalDefaultConfig,
  getResumeDetails,
} from '@/utils/helper/resume/index'
import { jobFormat } from './utils'

Page(class extends wx.$.Page {
  data = {
    /** 当前全职集合 */
    jobFull: [],
    /** 当前兼职集合 */
    jobPart: [],
  }

  useStore(state: StoreRootState) {
    const { subCount,
      subPartCount,
      subFullCount } = state.storage.myResumeDetails

    const { resumeSubMaxNum,
      maxFTPositionNum,
      maxPTPositionNum } = state.resume.globalConfig.basicConfigResp

    return {
      /** 当前兼职总数量 */
      subPartCount,
      /** 当前全职总数量 */
      subFullCount,
      /** 当前求职总数量 */
      subCount,
      /** 最大可添加的期望职位 */
      resumeSubMaxNum,
      /** 全职期望最大职位 */
      maxFTPositionNum,
      /** 兼职期望最大职位 */
      maxPTPositionNum,
    }
  }

  onLoad() {
    // 刷新工种tab
    dispatch(actions.recruitIndexActions.setState({ classifyTabIdInit: true }))
    // 获取全局通用配置
    getGlobalDefaultConfig()
    // 拿到本地存储的求职期望（优化）
    const details = store.getState().storage.myResumeDetails
    const { resumeUuid } = details || {}
    if (resumeUuid) {
      const info = jobFormat(details.basicResp.subs)
      wx.hideLoading()
      this.setData({
        jobFull: info.jobFull,
        jobPart: info.jobPart,
      })
    }
  }

  onShow() {
    // 获取求职期望
    wx.$.loading()
    getResumeDetails().then(resData => {
      const info = jobFormat(resData.basicResp.subs)
      wx.hideLoading()
      this.setData({
        jobFull: info.jobFull,
        jobPart: info.jobPart,
      })
    }).catch(() => {
      wx.hideLoading()
    })
  }

  onAdd() {
    const { subCount,
      resumeSubMaxNum,
      subFullCount,
      subPartCount,
      maxFTPositionNum,
      maxPTPositionNum } = this.data as DataTypes<typeof this>

    // 由于海投融合，resumeSubMaxNum 可能大于 maxFTPositionNum + maxPTPositionNum，但实际最大可添加数量应以 maxFTPositionNum + maxPTPositionNum 为准
    const realMaxNum = Math.min(resumeSubMaxNum, maxFTPositionNum + maxPTPositionNum)
    if (subCount >= realMaxNum) {
      wx.$.msg('已达上限')
      return
    }
    wx.$.r.push({
      path: '/subpackage/resume/resume_publish/complete/two/index',
      query: {
        type: '1', // 1.添加 2.编辑
        origin: 'edit',
        /** 全职可选数量 */
        numFull: maxFTPositionNum - subFullCount,
        /** 兼职可选数量 */
        numPart: maxPTPositionNum - subPartCount,
      },
    })
  }

  onEdit(e) {
    const { item } = e.currentTarget.dataset
    const { subCount,
      subFullCount,
      subPartCount,
      maxFTPositionNum,
      maxPTPositionNum } = this.data as DataTypes<typeof this>
    let industries = ''
    let mode = ''
    if (item.occupationInfo) {
      mode = item.occupationInfo.mode
      if (wx.$.u.isArrayVal(item.occupationInfo.industries)) {
        industries = item.occupationInfo.industries.join(',')
      }
    }
    wx.$.r.push({
      path: '/subpackage/resume/resume_publish/complete/two/index',
      query: {
        type: '2', // 1.添加 2.编辑
        origin: 'edit',
        mode,
        industries,
        resumeSubUuid: item.resumeSubUuid || '',
        occIds: item.occupationInfo.occId || '',
        /** 子名片总数 */
        numMax: subCount,
        /** 全职可选数量 */
        numFull: maxFTPositionNum - subFullCount,
        /** 兼职可选数量 */
        numPart: maxPTPositionNum - subPartCount,
        positionType: item.positionType,
      },
    })
  }
})
