.filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f6fa;
  padding: 0 24rpx;
}

.fixed.filter {
  background-color: #fff;
}

.hidden {
  display: none !important;
}

.inline-flex {
  display: flex;
  align-items: center;
}

.mr-24 {
  margin-right: 24rpx;
}

.item {
  position: relative;
  display: flex;
  padding: 16rpx 24rpx 16rpx 0rpx;
  justify-content: center;
  align-items: flex-end;
  font-size: 28rpx;
  overflow: hidden;
  .text {
    margin-right: 8rpx;
  }
}

.max-length {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 178rpx;
}

.def-g {
  padding: 8rpx 16rpx;
  margin-top: 12rpx;
  margin-bottom: 12rpx;
  background: rgba(245, 246, 250, 1);
  border-radius: 8rpx;
}

.slted-g {
  background: rgba(224, 243, 255, 1);
}

.tip-text {
  display: flex;
  align-items: center;
  height: 56rpx;
  background: #0092ff;
  padding: 0 10rpx 0 22rpx;
  border-radius: 8rpx;

  .text-value {
    white-space: nowrap;
    color: #fff;
    font-size: 24rpx;
  }
}

.tip-wrapper-shx {
  position: absolute;
  left: 0;
  bottom: -88rpx;
  transform: translateX(-72%);

  .arrowhead-shx::before {
    content: "";
    position: absolute;
    top: -26rpx;
    right: 9%;
    width: 0;
    height: 0;
    background: transparent;
    border: 14rpx solid transparent;
    border-bottom-color: #0092ff;
  }
}

.vipIcon {
  position: absolute;
  top: 0;
  right: 0;
  width: 24rpx;
  height: 24rpx;
}

.icon {
  width: 100%;
  height: 100%;
  transform: translateY(-8rpx);
}
