.body {
  background-color: #f5f6fa;
  min-height: 100vh;
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  border-bottom: 1rpx solid #f5f5f5;
}

.pd-lr {
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.back-icon {
  position: absolute;
  left: 0;
}

.header-search {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32rpx;
  padding-left: 38rpx;
}

.box {
  margin: 8rpx 0;
}

.search-cont {
  width: 100%;
  overflow: hidden;
}

.empty {
  padding-top: 16vh;
}

.empty-text {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.45);
}

.empty-img {
  width: 290rpx !important;
  height: 290rpx !important;
}

.list-img {
  width: 296rpx !important;
  height: 290rpx !important;
}

.logisticsTitle_word {
  width: 100%;
  height: 44rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 26rpx;

  .text {
    text-align: center;
    line-height: 44rpx;
  }
}

.tj-data-img {
  width: 296rpx !important;
  height: 290rpx !important;
}

.tj-title {
  font-size: 32rpx !important;
  font-weight: bold !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

.tj-text {
  font-size: 28rpx !important;
  color: rgba(0, 0, 0, 0.45) !important;
}

.group-class {
  padding: 12rpx 24rpx !important;
}

.group-component-class {
  background-color: #fff !important;
}

.cp-fixed-db {
  border-radius: 0;
  border-top: 1rpx solid rgba(233, 237, 243, 1);
}