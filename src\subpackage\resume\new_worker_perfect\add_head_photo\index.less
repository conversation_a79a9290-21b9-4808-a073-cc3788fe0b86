page {
  background-color: #fff;
  width: 100%;
  height: 100vh;
}
.container {
  width: 100%;
  padding: 0 32rpx;
  height: 84vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .top-box {
    margin-top: 12vh;
    .img-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 256rpx;
      .center-box {
        position: relative;
        width: 256rpx;
        height: 100%;
        .default-img-box {
          width: 256rpx;
          height: 100%;
          border-radius: 50%;
          background-color: rgba(0, 146, 255, 1);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .img {
            width: 96rpx;
            height: 96rpx;
          }
          .text {
            font-size: 26rpx;
            color: rgba(255, 255, 255, 1);
            margin-top: 16rpx;
          }
        }
        .default-img {
          width: 256rpx;
          height: 100%;
          border-radius: 50%;
          object-fit: contain;
        }
        .edit-img {
          position: absolute;
          bottom: 0;
          right: 0;
          z-index: 2;
          width: 72rpx;
          height: 72rpx;
        }
      }
    }
    .img-top-text {
      width: 100%;
      font-size: 50rpx;
      margin-top: 10%;
      font-weight: bold;
      text-align: center;
    }

    .img-des-text {
      width: 100%;
      color: rgba(0, 0, 0, 0.65);
      font-size: 26rpx;
      margin-top: 4.5%;
      text-align: center;
    }
  }


  .bottom-box {
    width: 686rpx;
    .wrap-top-box {
      font-size: 26rpx;
      display: flex;
      height: 48rpx;
      width: 100%;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      position: relative;
      .line {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        display: flex;
        align-items: center;
        &::before {
          content: '';
          width: 100%;
          display: block;
          height: 2rpx;
          margin: 0 32rpx;
          background-color: #e9edf3;
        }
      }
      .text {
        max-width: 70%;
        color: rgba(0, 0, 0, 0.65);
        padding: 0 48rpx;
        position: relative;
        z-index: 2;
        text-align: center;
        background-color: #FFF;
      }
    }

    .img-wrap {
      width: 100%;
      height: 144rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8%;
      margin-bottom: 13.6%;
      padding: 0 19rpx;

      .img-cover-item {
        width: 144rpx;
        height: 144rpx;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        box-sizing: border-box;
        margin-right: 24rpx;
        .img {
          width: 128rpx;
          height: 128rpx;
          border-radius: 50%;
        }
        .choose-img {
          position: absolute;
          right: 0;
          top: 0;
          width: 38rpx;
          height: 38rpx;
          z-index: 2;
          background-size: contain; /* 或者使用 cover */
          background-position: center; /* 居中显示 */
          background-repeat: no-repeat; /* 防止重复 */
        }
        &:last-of-type {
          margin-right: 0 !important;
        }
      }
      .blue {
        width: 144rpx;
        height: 144rpx;
        border: 2rpx solid rgba(0, 146, 255, 1);
      }
    }

    .footer {
      display: flex;
      width: 100%;
      padding: 0 30rpx;
    }

    .footer-mr {
      margin-top: 12%;
    }

    .f-btn {
      .btn();
      font-size: 38rpx;
      font-weight: bold;
      color: white;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      height: 112rpx;
      text-align: center;
      background: #0092ff;
      border-radius: 1998rpx;
      &:active {
        opacity: 0.8;
      }
    }

    .btn-next {
      flex: 1;
      .triangle-img {
        width: 24rpx;
        height: 24rpx;
        margin-left: 12rpx;
      }
    }

    .disabled {
      background-color: rgba(153, 211, 255, 1) !important;
      color: rgba(255, 255, 255, 0.45) !important;
    }
  }
}
