.job-expect {
  min-height: 100vh;
  background: #fff;
}

.cell {
  padding: 32rpx;
}

.cell-title {
  font-size: 38rpx;
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: bold;
  line-height: 54rpx;
  padding-bottom: 24rpx;
}

.cell-content {
  width: 100%;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(233, 237, 243, 1);
  box-sizing: border-box;
}

.title-tip {
  color: rgba(0, 0, 0, 0.45);
}

/* 置灰*/


.current-area-box {
  position: relative;
  margin-top: 24rpx;
}

.current-area {
  display: flex;
  align-items: center;
  width: 304rpx;
  height: 72rpx;
  background: #E0F3FF;
  padding: 0 24rpx;
  border-radius: 16rpx;
  white-space: nowrap;
  color: #0092ff;
  font-size: 26rpx;
}

.current-area::before {
  content: '';
  position: absolute;
  left: 30rpx;
  top: -16rpx;
  width: 0;
  height: 0;
  background: transparent;
  border: 10rpx solid transparent;
  border-width: 8rpx 10rpx;
  border-bottom-color: #E0F3FF;
}

.loc-icon {
  margin-right: 16rpx;
}

.rec-pos {
  color: rgba(0, 0, 0, 0.85);
  font-size: 26rpx;
  line-height: 36rpx;
  padding-top: 32rpx;
  padding-bottom: 24rpx;
}

.job-list-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(218rpx, 1fr));
  gap: 16rpx;
}

.job-content {
  width: 218rpx;
  padding: 16rpx 12rpx;
  border-radius: 8rpx;
  background: #f5f7fc;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
}

.job-text {
  line-height: 40rpx;
  .textrow(2);
}

.selected {
  background-color: #E0F3FF;
  color: #0092ff;
  font-weight: bold;
}

.custom-content-text {
  font-size: 34rpx !important;
  color: rgba(0, 0, 0, 0.85) !important;
  font-size: 34rpx !important;
  line-height: 48rpx !important;
  padding-right: 32rpx !important;
}

//置灰
.custom-text-disable {
  color: rgba(0, 0, 0, 0.25) !important;
}

