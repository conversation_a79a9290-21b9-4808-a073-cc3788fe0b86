<view class="job-expect">
  <worker-perfect-title title="想找什么工作？" prevPath="{{prevPath}}" />

  <view class="cell">
    <m-form id="form-job-expect" change>
      <view class="cell-title">你理想的工作城市是</view>
      <view class="cell-content">
        <m-form-city-bottom
          symbol="、"
          disabledIds="{{disabledIds}}"
          pointSource="11"
          custom-class-value="custom-content-text"
          custom-class-placeholder="custom-content-text custom-text-disable"
          multi
          max="{{maxCityNum}}"
          require
          requireTip="至少选择一项"
          placeholder="请选择期望工作城市"
          rightIcon="yp-mid_jt"
          rightIconColor="rgba(0, 0, 0, 0.45)"
          name="provinces"
          bind:change="onCityChange"
        />
      </view>

      <view wx:if="{{locationShow}}" class="current-area-box" bind:tap="onClickLocation">
        <view class="current-area"><icon-font type="yp-loc" custom-class="loc-icon" size="25rpx" color="#0092FF" />使用当前定位城市</view>
      </view>
    </view>

    <view class="cell">
      <view class="cell-title">你的期望职位是 <text class="title-tip">【多选】</text></view>
      <view class="cell-content">
        <m-form-classify-bottom
          custom-label="custom-content-text"
          custom-class-placeholder="custom-text-disable"
          rightIcon="yp-mid_jt"
          rightIconColor="rgba(0, 0, 0, 0.45)"
          placeholder="请选择期望职位"
          title="选择期望职位"
          sceneType="{{1}}"
          isAb="{{false}}"
          isShowRecommend
          maxSelectNum="{{publishOccupationCountLimit}}"
          isRadioChoose
          name="occupations"
          change
          bind:change="onOccChange"
          sourcePageName="完善引导流程"
          symbol="、"
        />
      </view>
      <block wx:if="{{recommendList.length}}">
        <view class="rec-pos">推荐职位</view>
        <view class="job-list-box">
          <view class="job-content {{item.selected ? 'selected' : ''}}" wx:for="{{recommendList}}" bind:tap="onRecommendChange" wx:key="id" data-id="{{item.id}}">
            <view class="job-text" >{{item.name}}</view>
          </view>
        </view>
      </block>
    </m-form>
  </view>
  <worker-perfect-footer
    isSkip="{{processConfig.jumpSwitch}}"
    disabled="{{!selectedCitys.length || !selectedRecommends.length}}"
    jumpPath="{{jumpPath}}"
    pageTitle="求职期望"
    processConfig="{{processConfig}}"
    bind:onSubmit="onSubmit"
    bind:jumpCallBackFn="jumpCallBackFn"
    btnText="{{btnText}}"
    />
</view>
