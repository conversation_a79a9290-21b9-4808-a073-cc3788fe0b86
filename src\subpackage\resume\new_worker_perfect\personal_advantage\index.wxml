 <!-- 个人优势页面 -->
<custom-header customBack bind:back="onBack" title="{{pageTitle}}" disableShowHomeBtn/>
<view class="body">
  <view bind:tap="onHideKey" class="head">
    <view class="title">分享一下你的个人优势吧</view>
  </view>
  <sensitive-textarea 
    style="{{bottomHeight > 10 && isFocus ?  ('height: calc(100vh - 91rpx - '+bottomHeight+  'px - '+ headerTopHeight + ')') : ('height: calc(100vh - '+ (isWarning ? warningHeight : 0 )+ 'px - '+ bottomFooterHeight + 'px - '+ headerTopHeight + ')')}} " 
    class="sensitive-textarea"
    id="sensitive"
    placeholder="你的第二名片，请用1-2句话向老板介绍自己"
    value="{{content}}"
    hold-keyboard
    placeholder-class="placeholder"
    maxlength="{{-1}}"
    adjustPosition="{{false}}"
    disableDefaultPadding
    showConfirmBar="{{false}}"
    focus="{{!content}}"
    bind:focus="onFocus"
    bind:heightChange="onHideKey"
    bind:input="onInput"
    bind:keyChange="onKeyChange"
    bind:longtap="onLongTap"
    />
  <warning-tip wx:if="{{isWarning && !isFocus}}" id="warning-tip" tip-class="warning-tip"  />
  <view class="footer-full"></view>
</view>
<view class="footer" id="content-footer" style="{{(bottomHeight > 10 && isFocus) ? 'padding-bottom: 0' : ''}};bottom: {{bottomHeight || 0}}px">
  <view class="info-text">
    <view bind:tap="onClear" class="clear {{content.length < 1 ? 'disabled' : ''}}">
      清空内容
    </view>
    <view class="info-num">
      <view class="{{content.length > maxContent ? 'num-err' : 'num'}} {{content.length < 1 && 'num-gray'}}">
        {{content.length || 0}}
      </view>
      <view class="num-gray">/{{maxContent}}</view>
    </view>
  </view>
  <view class="btn-group" wx:if="{{bottomHeight == 0}}">
    <button bind:tap="onJump" class="f-btn btn-jump" wx:if="{{processConfig.jumpSwitch}}" data-page-title="工作经历-个人优势" data-texts="跳过">跳过</button>
    <button bind:tap="onNext" class="f-btn btn-next {{content.length == 0 ? 'disabled-btn' : ''}}" data-page-title="工作经历-个人优势" data-texts="{{btnText}}">{{btnText}}</button>
  </view>
</view>


