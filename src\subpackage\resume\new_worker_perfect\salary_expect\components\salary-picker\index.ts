Component(class extends wx.$.Component {
  properties = {
    /** 薪资数据 */
    salary: { type: null, value: [] },
    /** 当前薪资范围 */
    value: { type: null, value: [] },
  }

  data = {
    rSalary: ['面议'],
    selectIndex: [0, 0],
    colStyle: 'line-height: 52px; text-align: center;font-weight: bold;font-size: 34rpx;',
    indicatorStyle: 'height: 52px;',
  }

  observers = {
    'value,salary': function () {
      const { value, salary } = this.data
      this.handlerSalary(value, salary)
    },
  }

  async handlerSalary(value, salary) {
    const rSalary = []
    const dSalary = salary || this.data.salary
    let startIndex = 0
    let endIndex = 0
    if (wx.$.u.isArrayVal(value)) {
      startIndex = value[0] ? dSalary.findIndex((item) => item === value[0]) : 0

      startIndex = startIndex === -1 ? 0 : startIndex

      if (startIndex != 0) { // rSalary只要后五项, 右侧数据的处理
        rSalary.push(...dSalary.slice(startIndex + 1, startIndex + 1 + 5))
        endIndex = value[1] ? rSalary.findIndex((item) => item === value[1]) : 0
        endIndex = endIndex === -1 ? 0 : endIndex
      }
    }
    if (startIndex == 0) {
      rSalary.push('面议')
    }
    // 设置右边的数据
    this.setData({ rSalary, selectIndex: [startIndex, endIndex], values: [startIndex, endIndex] })
    // 选中的索引, 为了解决picker-view的选中问题所以延迟加载
    await wx.$.u.wait(100)

    this.setData({
      selectIndex: [startIndex, endIndex],
      values: [startIndex, endIndex],
    })
  }

  /** change事件 */
  onChange(e) {
    const { value } = e.detail
    const { salary, rSalary } = this.data as DataTypes<typeof this>
    const startValue = salary[value[0]] || 0
    let endValue = rSalary[value[1]] || 0
    // 如果滑动的是左边的模块
    if (value[0] != this.data.selectIndex[0]) {
      // 处理右侧数据回到第一个数据
      endValue = value[0] == 0 ? '面议' : salary[value[0] + 1]
      value[1] = 0
    }

    this.triggerEvent('change', {
      value: [startValue, endValue],
    })
  }

  onStart() {
    this.triggerEvent('start')
  }

  onEnd() {
    this.triggerEvent('end')
  }
})
