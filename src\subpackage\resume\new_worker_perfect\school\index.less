page {
  width: 100%;
  padding: 0 32rpx;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.title-top {
  width: 100%;
  font-size: 50rpx;
  margin: 32rpx 0;
  font-weight: bold;
}

.input-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .input-left {
    flex: 1;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(245, 247, 252, 1);
    border-radius: 16rpx;
    padding-left: 24rpx;

    .sensitive-input {
      flex: 1;
      margin-left: 16rpx;
    }

    .input-clear {
      opacity: 0.7;
      padding: 6rpx 24rpx;
      display: inline-flex;
    }
  }
}


.search-manuel {
  position: fixed;
  z-index: 5;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 0 32rpx;
  background: white;
  padding-bottom: calc(144rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));

  .search-item {
    height: 112rpx;
    padding: 34rpx 0;
    font-size: 30rpx;
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1rpx solid rgba(233, 237, 243, 1);

    .search-item-title {
      .textrow(1);
      overflow: hidden;
      word-break: break-all;
    }
  }
}
.hidden {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}

::-webkit-scrollbar {
  display: none;
}


.footer {
  display: flex;
  margin: -20rpx;
}

.f-btn {
  .btn();
  margin: 10rpx !important;

  font-size: 34rpx;
  font-weight: bold;
  color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  text-align: center;
  background: #0092ff;
  border-radius: 12rpx;
  &:active {
    opacity: 0.8;
  }
}

.btn-next {
  flex: 1;
}

.btn-jump {
  width: 220rpx !important;
  background: #f5f6fa;
  color:rgba(0, 0, 0, 0.65);
}

.disabled {
  background-color: rgba(153, 211, 255, 1) !important;
  color: rgba(255, 255, 255, 0.45) !important;
}