import { dealDialogRepByApi } from '@/utils/helper/dialog/index'
import { autoSubmitProcess, getLastNextBtnText, getResumeProcessData, resumeProcessSkip } from '@/utils/helper/resume/utils'
import { actions, dispatch } from '@/store/index'
import { tools } from '@/utils/index'
import { getDom } from '@/utils/tools/common/index'

Page(class extends wx.$.Page {
  data = {
    info: {},
    maxContent: 1600,
    /** 输入框的值 */
    content: '',
    pageTitle: '',
    /** 是否获取了焦点 */
    isFocus: false,
    /** 键盘高度 */
    bottomHeight: 0,
    /** 上一个页面 */
    prevPath: '',
    /** 下一个页面 */
    nextPath: '',
    /** 跳过目的页面 */
    jumpPath: '',
    /** 模块配置数据 */
    processConfig: { jumpSwitch: false, type: '' },
    /** 下一步的文案 */
    btnText: '下一步',
    /** 顶部banner的高度 */
    headerTopHeight: tools.common.getHeaderHeight(),
    /** 底部的高度 */
    bottomFooterHeight: 0,
    /** 是否有敏感词 */
    isWarning: false,
    /** 敏感词高度 */
    warningHeight: 0,
  }

  async onLoad(options) {
    const params = wx.$.r.getParams()
    const headerTop = tools.common.getHeaderHeight('140rpx', true)
    const processWholeData = await getResumeProcessData()
    const btnText = getLastNextBtnText(processWholeData.processConfig.name)
    this.setData({
      query: options,
      headerTopHeight: headerTop,
      ...params,
      ...processWholeData,
      content: processWholeData?.moduleValues?.detail || '',
      btnText,
    })

    setTimeout(() => {
      wx.nextTick(async () => {
        const footer = (await getDom.call(this, '#content-footer')) || {} as any
        this.setData({ bottomFooterHeight: footer.height })
      })
    }, 200)
  }

  /** 点击返回按钮逻辑 */
  onBack() {
    const { prevPath } = this.data
    this.sensitiveHideKey()
    wx.$.r.replace({
      path: prevPath,
    })
  }

  onFocus(e) {
    this.setData({
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    })
  }

  onLongTap(e) {
    if (!this.data.isFocus) {
      this.setData({
        isFocus: true,
        bottomHeight: e.detail.height || 0,
      })
    }
  }

  /** 收起键盘 */
  onHideKey(e) {
    if (e?.type !== 'heightChange') {
      return
    }
    this.setData({ isFocus: false, bottomHeight: 0 })
    const { warningHeight, isWarning } = this.data
    if (isWarning && !warningHeight) {
      this.getWarningHeight()
    }
  }

  onInput(e) {
    this.setData({ content: e.detail.value })
  }

  onClear() {
    if (this.data.content) {
      this.handleInputUpdate()
    }
    this.sensitiveHideKey()
  }

  /** 下一步 */
  onNext() {
    const { content, nextPath, processConfig } = this.data
    const pageName = processConfig?.name || ''

    if (content.length > 0) {
      if (content.length > 1600) {
        wx.$.msg('已超出最大字数限制')
        return
      }
      wx.showLoading({ title: '提交中...' })
      wx.$.javafetch['POST/resume/v3/prepub/pubWorkExp']({
        itemId: 8,
        detail: content,
      })
        .then(res => {
          wx.hideLoading()
          if (res.code == 0) {
            dispatch(actions.resumeActions.setProcessValue({ [processConfig.type]: { detail: content } }))
            autoSubmitProcess(pageName)
            && wx.$.r.replace({
              path: nextPath,
            })
          }
        }).catch(() => {
          wx.hideLoading()
        })
    } else {
      wx.$.msg('请填写工作内容')
    }
  }

  /** 跳过 */
  async onJump() {
    const { content, jumpPath, processConfig }: any = this.data

    // 注：如果【最高学历】页为关闭状态，则跳转到【个人优势】页，如果【个人优势】页为关闭状态，则跳转到【添加头像】页，如果【添加头像】页为关闭状态，则跳转到【找工作】列表页
    if (content.length > 0) {
      const popup = await dealDialogRepByApi('gznrtgtxtc')
      wx.$.showModal({
        ...popup,
        success: (res) => {
          // 点击确定按钮
          if (res.jumpEventType != 3) {
            resumeProcessSkip(processConfig.type, processConfig.name, jumpPath)
          }
        },
      })
    } else {
      resumeProcessSkip(processConfig.type, processConfig.name, jumpPath)
    }
  }

  /** 敏感词 */
  async onKeyChange(e) {
    this.setData({
      isWarning: !!e.detail,
    })
    e.detail && this.getWarningHeight()
  }

  /** 获取warning-tip高度 */
  async getWarningHeight() {
    if (!this.data.warningHeight) {
      const dom = await getDom('#warning-tip')
      // 如果没拿到高度就再获取一次
      if (!dom?.height) {
        setTimeout(async () => {
          const dom = await getDom('#warning-tip')
          this.setData({
            warningHeight: dom?.height || 0,
          })
        }, 100)
        return
      }
      this.setData({
        warningHeight: dom?.height || 0,
      })
    }
  }

  /** 通过输入框组件更新数据 */
  handleInputUpdate() {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.updateValue()
      this.setData({ content: '', isWarning: false })
    })
  }

  /** 收起敏感词输入框键盘 */
  sensitiveHideKey() {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.setData({ inputFocus: false })
    })
  }
})
