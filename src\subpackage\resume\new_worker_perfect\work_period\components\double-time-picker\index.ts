/*
 * @Date: 2022-8-11 14:00:00
 * @Description: 时间选择器- 双模式
 */

import dayjs from '@/lib/dayjs/index'

const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
Component(class extends wx.$.Component {
  properties = {
    query: { type: Object, value: {} },
    items: { type: Array, value: [] },
    value: { type: Object, value: [] },
    placeholder: { type: String, value: '请选择' },
    disabled: { type: Boolean, value: false },
    /** 初始化回调值 */
    needInitValue: { type: Boolean, value: false },
    /** 左侧的title */
    leftTitle: { type: String, value: '' },
    /** 右侧的title */
    rightTitle: { type: String, value: '' },
  }

  data = {
    leftYears: [],
    rightYears: [],
    l_months: months,
    r_months: months,
    leftValues: [],
    rightValues: [],
    colStyle: 'line-height: 52px; text-align: center;font-weight: bold;font-size: 34rpx;',
    indicatorStyle: 'height: 52px;',
    // 当前月份
    curmonth: 0,
    left_startYear: 0,
    left_endYear: 0,
    right_startYear: 0,
    right_endYear: 0,
    leftChangeValue: [],
    rightChangeValue: [],
  }

  observers = {
    value(v) {
      let { leftYears, rightYears } = this.data
      const { left_startYear, left_endYear, right_startYear, right_endYear } = this.data
      if (!wx.$.u.isArrayVal(leftYears)) {
        leftYears = this.onInitYear(true, 'left')
      }
      if (!wx.$.u.isArrayVal(rightYears)) {
        rightYears = this.onInitYear(false, 'right')
      }
      let leftValues = []
      let rightValues = []

      if (v.length > 0 && v?.[0] && v?.[1]) {
        const leftTimeStr = v?.[0] ? formatMonth(v[0]) : ''
        const rightTimeStr = v?.[1] ? formatMonth(v[1]) : ''
        let left_m = months

        console.log('djsdhdsjsaqqqqqqqqqqqqqqqqqqqqqq', leftTimeStr.substring(0, 4))
        if (leftTimeStr.substring(0, 4) == dayjs().format('YYYY')) {
          left_m = months.filter(m => m <= dayjs().format('MM'))
        } else if (leftTimeStr.substring(0, 4) < '1990') {
          left_m = []
        }

        let right_m = months
        if (rightTimeStr.substring(0, 4) == dayjs().format('YYYY')) {
          right_m = months.filter(m => m <= dayjs().format('MM'))
        } else if (rightTimeStr.substring(0, 4) > dayjs().format('YYYY')) {
          right_m = []
        }
        this.setData({ r_months: right_m, l_months: left_m })

        leftValues = this.formatHaveValueYears(leftTimeStr, left_m, leftYears, left_startYear, left_endYear)
        rightValues = this.formatHaveValueYears(rightTimeStr, right_m, rightYears, right_startYear, right_endYear)
        const params: any = { leftValues, rightValues, leftChangeValue: leftValues, rightChangeValue: rightValues }
        this.setData(params)
      }
    },
  }

  pageLifetimes = {
    show() {
      if (this.data.value?.length > 0 && this.data.value?.[0] && this.data.value?.[1]) {
        return
      }
      // 默认选项：入职时间及离职时间均默认选择当前年月
      const { needInitValue } = this.data
      let { leftYears, rightYears } = this.data
      if (needInitValue) {
        if (!wx.$.u.isArrayVal(leftYears)) {
          leftYears = this.onInitYear(true, 'left')
        }
        if (!wx.$.u.isArrayVal(rightYears)) {
          rightYears = this.onInitYear(false, 'right')
        }
        const filter_m = months.filter(m => m <= dayjs().format('MM'))
        const empty_m = []
        this.triggerEvent('change', { value: [`${leftYears[0]}-${filter_m[filter_m.length - 1]}`, `${rightYears[0]}-0`] })
        // 默认左边选到当年当月，右边选择至今，没有月份可选
        this.setData({ leftValues: [0, filter_m.length - 1], rightValues: [0, 0], leftChangeValue: [0, filter_m.length - 1], rightChangeValue: [0, 0], l_months: filter_m, r_months: empty_m })
      }
    },
  }

  formatHaveValueYears(v, m, current_years, startYear, endYear) {
    let values = []
    let months = m
    const { curmonth } = this.data
    // 按 '-' 分隔字符串
    const [year, month] = v?.split('-').map(Number) || [1990, 0]

    const yearIdx = current_years.findIndex(y => y == year)
    if (curmonth > 0) {
      if (year == endYear) {
        months = months.filter((m) => m <= curmonth)
      } else if (year == startYear) {
        months = months.filter((m) => m >= curmonth)
      }
    }

    const monthIdx = months.length > 0 ? months.findIndex(m => m == month) : 0
    values = [yearIdx, monthIdx]
    return values
  }

  onInitYear(isEntry: boolean, type: string) {
    const years = []
    const currentYear = Number(dayjs().format('YYYY'))

    let startYear; let endYear

    if (isEntry) {
      // 入职时间：从 1990 年前到当前年份
      startYear = 1990 - 1 // 1990年前
      endYear = currentYear
    } else {
      // 离职时间：从 1990 年到至今 (1990-当前年份+1,当前年份要显示出来， 并且要多显示个 '至今' )
      startYear = 1990
      endYear = currentYear + 1
    }

    for (let i = startYear; i <= endYear; i++) {
      years.unshift(i)
    }
    if (type == 'left') {
      this.setData({ leftYears: years, left_startYear: startYear, left_endYear: endYear })
    } else {
      this.setData({ rightYears: years, right_startYear: startYear, right_endYear: endYear })
    }

    return years
  }

  onChangesLeft(e) {
    const { leftYears, r_months, rightYears, rightChangeValue } = this.data
    const { value } = e.detail

    // 如果年份选择了“1990年以前”，则不支持选择月份。如果是当年，不能超过最大月份。
    let m = months
    if (value[0] === leftYears.length - 1) {
      m = []
    } else if (value[0] === 0) {
      m = months.filter(m => m <= dayjs().format('MM'))
    }

    this.setData({ leftChangeValue: value, l_months: m })

    this.triggerEvent('change', { value: [`${leftYears[value[0]]}-${m?.[value[1]] || 0}`, `${rightYears[rightChangeValue[0]]}-${r_months[rightChangeValue[1]] || 0}`] })
  }

  onChangesRight(e) {
    const { leftYears, rightYears, l_months, leftChangeValue } = this.data
    const { value } = e.detail
    let m = months
    let right_month_valueIndex = m[value[1]]
    // 至今。 也不显示月份
    if (value[0] === 0) {
      m = []
      right_month_valueIndex = 0
    } else if (value[0] == 1) {
      // 如果年份选择当年，则不超过最大月份。
      m = months.filter(m => m <= dayjs().format('MM'))
      if (value[1] == m.length && value[1] != 0) {
        right_month_valueIndex = m[m.length - 1]
      }
    }

    this.setData({ rightChangeValue: value, r_months: m })

    this.triggerEvent('change', { value: [`${leftYears[leftChangeValue[0]]}-${l_months[leftChangeValue[1]] || 0}`, `${rightYears[value[0]]}-${right_month_valueIndex}`] })
  }
})

function formatMonth(dateString: string): string {
  const [yearPart, monthPart] = dateString.split('-').map(Number)

  // 特殊处理月份为 0 的情况
  if (monthPart === 0) {
    return `${yearPart}-0`
  }

  // 正常处理其他月份
  const date = dayjs(`${yearPart}-${monthPart}`)
  const year = date.year()
  const month = date.month() + 1 // dayjs 月份从 0 开始
  return `${year}-${month < 10 ? month : month}`
}
