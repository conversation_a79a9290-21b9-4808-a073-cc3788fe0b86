.full {
  height: 120rpx;
  background-color: #FFF;
  box-sizing: content-box;
  padding-bottom: calc(48rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(48rpx + env(safe-area-inset-bottom));
  &.body-hide {
    display: none;
  }
}

.body {
  position: fixed;
  z-index: 99;
  left: 32rpx;
  right: 32rpx;
  height: 120rpx;
  bottom: -130rpx;
  transition: bottom 0.3s ease-in-out;
  &.body-show {
    bottom: calc(48rpx + constant(safe-area-inset-bottom));
    bottom: calc(48rpx + env(safe-area-inset-bottom));
  }
  &.body-hide {
    bottom: -130rpx;
  }
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  width: 100%;
  overflow: hidden;
}

.btn-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 120rpx;
  flex: 1;
  position: relative;
  font-size: 26rpx;
  &.btn-line::before {
    content: '';
    display: block;
    position: absolute;
    right: 0;
    width: 2rpx;
    height: 32rpx;
    background-color: #e9edf3;
  }
  .mini-text {
    padding-top: 8rpx;
    text-align: center;
  }
}

.text {
  text-align: center;
  font-weight: 500;
  font-size: 34rpx;
  line-height: 48rpx;
  padding-left: 16rpx;
}

.menu {
  position: absolute;
  right: 0;
  top: 0;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    width: 2rpx;
    height: 32rpx;
    background-color: rgba(233, 237, 243)
  }
}

.img {
  width: 48rpx;
  height: 48rpx;
}

.anime-cont {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  border-radius: 64rpx;
  background-color: #FFF;
  box-shadow: 0rpx 0rpx 64rpx rgba(0, 0, 0, 0.16);
  height: 120rpx;
}

.anime {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  width: 0;
  opacity: 0;
}

.anime-a {
  &.anime-one {
    /* 停留在动画的最后一帧*/
    animation: anime-show 0.5s forwards;
    z-index: 2;
  }

  &.anime-two {
    /* 停留在动画的最后一帧*/
    animation: anime-hide 0.25s forwards;
    z-index: 1;
  }
}

.anime-b {
  width: 100%;
  opacity: 1;

  &.anime-one {
    /* 停留在动画的最后一帧*/
    animation: anime-hide 0.25s forwards;
    z-index: 1;
  }

  &.anime-two {
    /* 停留在动画的最后一帧*/
    animation: anime-show 0.5s forwards;
    z-index: 2;
  }
}

/** 缩短元素宽度，当宽度小于 50% 时隐藏 */

@keyframes anime-show {
  0% {
    width: 85%;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

@keyframes anime-hide {
  0% {
    width: 100%;
    opacity: 1;
  }
  100% {
    width: 85%;
    opacity: 0;
  }
}
