.placeholder {
  color: rgba(0, 0, 0, 0.25);
}


.list-item-tags {
  margin-top: 8rpx;

  .tag-box {
    height: 272rpx;
    overflow: hidden;

    .tag-cover {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      /* .tag {*/
      /*   height: 56rpx;*/
      /*   display: flex;*/
      /*   align-items: center;*/
      /*   justify-content: center;*/
      /*   padding: 0 16rpx;*/
      /*   border-radius: 8rpx;*/
      /*   background-color: #f5f6fa;*/
      /*   font-size: 26rpx;*/
      /*   color: rgba(0, 0, 0, 0.85);*/
      /*   margin-right: 16rpx;*/
      /*   margin-bottom: 16rpx;*/
      /* }*/
    }
  }

  .more-height {
    height: auto;
    overflow: visible;
  }

  .more-box{
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    height: 44rpx;
    margin-top: 40rpx;
    .text {
      color: rgb(0, 146, 255);
      font-size: 28rpx;
    }
  }
}

.hidden-box {
  position: fixed;
  bottom: -280rpx;
  height: auto !important;
}


.tag {
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
  border-radius: 8rpx;
  background-color: #f5f6fa;
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.85);
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}