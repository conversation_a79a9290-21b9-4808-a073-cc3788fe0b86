.body {
  background: #fff;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
}

.placeholder {
  padding-bottom: 40rpx;
  color: rgba(0, 0, 0, 0.25);
}

.cell-card {
  padding: 0 !important;
}

.interview-video {
  position: relative;
  height: 360rpx;
  border-radius: 8rpx;
  background: #f5f6fa;
  overflow: hidden;
}

.interview-video-visible {
  padding: 0 16rpx 0 24rpx;
  margin-top: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 96rpx;
  background: #F5F6FA;
  border-radius: 8rpx;
}

.left {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
}

.left-icon {
  margin-right: 8rpx;
}

.right {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #0092FF;
}

.right-btn {
  margin-left: 8rpx;
  font-weight: 400;
}

.play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 80rpx;
  height: 80rpx;
}

.cover-img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 622rpx;
  height: 360rpx;
  opacity: 0.7;
  transform: scale(1.3);
}

.video-poster {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 1;
  transform: translateX(-50%);
  width: 210rpx;
  height: 360rpx;
  background: #f5f6fa;
}

.video-duration {
  padding: 10rpx 12rpx;
  line-height: 28rpx;
  position: absolute;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  bottom: 10rpx;
  right: 10rpx;
  z-index: 1;
  text-align: center;
}

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 72rpx;
  background: #F5F6FA;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #0092FF;
  width: 100% !important;
  margin-top: 16rpx;
}

.interview-fail {
  width: 100%;
  padding: 24rpx;
  background: #f5f6fa;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.fail-head {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  /* color: #0092ff; */
  color: #f74742;
  font-weight: 700;
  line-height: 44rpx;
}

.fail-head-set {
  display: inline-flex;
  align-items: center;
}

.fail-reason {
  padding-top: 16rpx;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.65);
  line-height: 40rpx;
}

