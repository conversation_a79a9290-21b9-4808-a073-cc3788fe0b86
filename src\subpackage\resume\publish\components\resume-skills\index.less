.body {
  background: #FFF;
  border-radius: 24rpx;
  padding-bottom: 24rpx;
}

/** 隐藏元素 */

.head {
  height: 112rpx;
  background: #FFF;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.cont-list {
  margin-bottom: -16rpx;
}

.cont-item {
  background-color: #E0F2FF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding-right: 24rpx;
  position: relative;
}

/** 加急服务背景 */
.cont-item-urgent-bg {
  width: 104rpx;
  height: 128rpx;
  position: absolute;
  right: 8rpx;
  top: 8rpx;
  pointer-events: none;
}

/** 刷新服务背景 */
.cont-item-refresh-bg {
  width: 120rpx;
  height: 120rpx;
  position: absolute;
  right: 0rpx;
  top: 4rpx;
  pointer-events: none;
}

.cont-item-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cont-item-tit {
  flex: 1;
  height: 60rpx;
  padding-left: 16rpx;
  font-weight: bold;
  line-height: 44rpx;
  color: rgba(255, 255, 255, 0.95);
  background-image: url('https://cdn.yupaowang.com/yupao_mini/yp-mini_wsxx.png');
  background-repeat: no-repeat;
  background-size: 188rpx 60rpx;
  font-size: 32rpx;
}

.cont-item-right {
  text-align: right;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 44rpx;
}

.cont-item-cont {
  padding: 8rpx 0 24rpx 24rpx;
  position: relative;
  z-index: 2;
}

.cont-text {
  font-weight: bold;
  line-height: 44rpx;
}

/** 进度条 */

/** 加急信息start */
.cont-item-urgent-info {
  padding-left: 24rpx;
}

.cont-item-urgent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
/** 加急信息end */

/** 按钮集合 */
.cont-btns {
  display: flex;
  padding-bottom: 24rpx;
  justify-content: flex-end;
}

/** 按钮折扣图片 */
.cont-btn-discount {
  position: relative;

  .discount-img {
    pointer-events: none;
    width: 64rpx;
    height: 48rpx;
    position: absolute;
    right: 0;
    top: -12rpx;
  }
}

/** 积分充值弹窗 */
