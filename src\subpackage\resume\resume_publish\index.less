page {
  background: rgba(255, 255, 255, 1);
}

.publish-content {
  padding: 24rpx 60rpx 0rpx;
  width: 100vw;
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.publish-head {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 48rpx;
}

.footer-btn {
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100vw;
  /* border-top: 1rpx solid rgba(0, 0, 0, 0.12);*/
}

.btn {
  padding-top: 24rpx;
  padding-bottom: 20rpx;
}

.btn-primary {
  width: 686rpx !important;
  font-weight: bold;
  font-size: 34rpx;
  border-radius: 12rpx;
}

.rule-tips {
  line-height: 40rpx;
  text-align: center;
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.45);
  width: 100%;
}

.publish-form {
  margin: 0;
}


.publish-form-item {
  display: flex;
  flex-direction: column;
  margin-top: 60rpx;
}

.form-label{
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 38rpx;
}

.form-conten{
  border-radius: 16rpx;
  border: 2rpx solid rgba(233, 237, 243, 1);
  background: rgba(255, 255, 255, 1);
  min-height: 104rpx;
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
}


.recommend {
  width: 100%;
  background-color: #F5F6FA;
  padding: 32rpx;
  margin-top: 32rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  margin-bottom: 24rpx;
}

.recommend-grids {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 20rpx -16rpx -16rpx -16rpx;

}

.recommend-item {
  padding: 10rpx 12rpx;
  background-color: #FFF;
  letter-spacing: 1rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin: 0 16rpx 16rpx;
}

.recommend-grids .selected {
  color: #0092FF;
  font-weight: bold;
  background-color: #E0F3FF;
}

.custom-label {
  font-size: 30rpx !important;
  min-width: 85vw !important;
}
