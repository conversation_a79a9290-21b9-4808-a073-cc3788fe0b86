page {
  background-color: #fff;
}

.hidden {
  display: none!important;
}

.sensitive-textarea {
  width: 100%;
  display: block;
  /* height: calc(100vh - 160rpx - 260rpx - 220rpx);*/
  /* padding-bottom: 32rpx;*/
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30rpx;
  line-height: 50rpx;
  color: rgba(0, 0, 0, 0.45);
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 32rpx;
  background-color: #FFF;
  .safe-area(24rpx);
  border-top: 1rpx solid #e9edf3;
}

.footer-full {
  height: 220rpx;
  .safe-area();
}

.disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  pointer-events: none !important;
}

.info-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20rpx;

  .clear {
    color: @primary-color;
  }
  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: @primary-color;
  }
  .num-err {
    color: @error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.btn {
  color: #FFF;
  font-size: 34rpx;
  height: 96rpx;
  line-height: 96rpx;
  display: flex;
  font-weight: bold;
  text-align: center;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: #0092ff;

  &.btn-mini{
    height: 72rpx;
    line-height: 72rpx;
    display: inline-flex;
    align-items: center;
    padding: 0 30rpx;
    margin-left: 24rpx;
  }
}
