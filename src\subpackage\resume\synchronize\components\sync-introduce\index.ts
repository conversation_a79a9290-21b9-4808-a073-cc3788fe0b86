/**
 * /*
 * @Date: 2024-10-16 19:02:55
 * @Description: 同步个人优势
 * @query: {
 *  }
 */

import { store } from '@/store/index'
import { dealDialogShow } from '@/utils/helper/dialog/index'
import { refreshMyInfo } from '@/utils/helper/resume/index'
import { getDom } from '@/utils/tools/common/index'

Component(class extends wx.$.Component {
  properties = {
    /** 是否是添加 */
    isAdd: { type: Boolean, value: false },
    /** 底部按钮文本 */
    footerText: { type: null, value: '' },
    /** 这个用来在外部收起键盘 */
    closeFocus: { type: null, value: '' },
    /** 接收的表单数据 */
    dataSource: { type: null, value: {} },
  }

  /** 监听 */
  observers = {
    closeFocus(val) {
      if (val) {
        this.sensitiveHideKey()
      }
    },
    dataSource(val) {
      if (val) {
        this.init()
      }
    },
  }

  data = {
    info: {},
    maxContent: 500,
    /** 输入框的值 */
    content: '',
    /** 旧的输入框的值 */
    contentOld: '',
    /** 是否获取了焦点 */
    isFocus: false,
    /** 键盘高度 */
    bottomHeight: 0,
    /** 是否有敏感词 */
    isWarning: false,
    /** 敏感词高度 */
    warningHeight: 0,
  }

  lifetimes = {
    ready() {
      this.init()
    },
  }

  init(options) {
    const { dataSource } = this.data as DataTypes<typeof this>
    this.setData({
      content: dataSource.introduce,
      contentOld: dataSource.introduce,
      bottomHeight: 0,
    })
  }

  /** 点击提交按钮 */
  async onSubmit() {
    await wx.$.u.waitAsync(this, this.onSubmit, [], 100)
    if (!this.saveBool()) {
      return
    }
    this.sensitiveHideKey()
    // wx.$.msg('保存成功')
    if (this.data.isAdd) {
      // 提交数据
      await this.submitRequest()
    } else {
      wx.$.nav.event({ introduce: this.data.content })
      wx.$.nav.back()
    }
  }

  /** 保存内容 */
  async submitRequest() {
    const { resumeUuid } = store.getState().storage.myResumeDetails
    const introduce = this.data.content
    wx.$.loading('保存中...')
    const res = await wx.$.javafetch['POST/resume/v3/perfect/introduce']({
      resumeUuid,
      introduce,
    }).catch((err) => err)
    wx.hideLoading()
    const { code, popup } = res || {}
    if (code != 0 && popup) {
      wx.$.showModal({
        ...popup,
      })
    } else if (res.code == 0) {
      this.triggerEvent('success', {
        message: '内容已保存',
        success: true,
      })
    } else {
      wx.$.msg(res.message || '保存失败')
    }
  }

  /** 判断输入的内容是否有效 */
  saveBool() {
    const { content, maxContent } = this.data
    if (content.length > maxContent) {
      wx.$.msg('已超出最大字数限制')
      return false
    }
    if (!content || content.trim() === '') {
      wx.$.msg('个人优势不能为空')
      return false
    }
    return true
  }

  /** 点击返回按钮逻辑 */
  onBack() {
    this.sensitiveHideKey()
    const { content, contentOld } = this.data
    if (content === contentOld) {
      wx.$.r.back()
      return
    }
    if (!this.data.content || `${this.data.content}`.trim() == '') {
      wx.$.r.back()
      return
    }

    dealDialogShow({
      dialogIdentify: 'jlwbcfhts',
    }).then(res => {
      const { itemClass, btnIndex } = res || { btnIndex: 0, itemClass: 'cancel' }
      if (itemClass == 'none') {
        wx.$.confirm({
          content: '内容尚未保存,确定退出?',
        }).then(() => {
          wx.$.r.back()
        }).catch(() => {})
        return
      }
      if (btnIndex == 0) {
        return
      }
      wx.$.r.back()
    })
  }

  onFocus(e) {
    this.setData({
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    })
  }

  onLongTap(e) {
    if (!this.data.isFocus) {
      this.setData({
        isFocus: true,
        bottomHeight: e.detail.height || 0,
      })
    }
  }

  onHeightChange(e) {
    this.setData({
      bottomHeight: e.detail.height || 0,
    })
  }

  /** 收起键盘 */
  onHideKey(e) {
    if (e?.type !== 'heightChange') {
      return
    }
    const { warningHeight, isWarning } = this.data
    if (isWarning && !warningHeight) {
      this.getWarningHeight()
    }
    this.setData({ isFocus: false, bottomHeight: 0 })
  }

  onInput(e) {
    this.setData({ content: e.detail.value })
  }

  onClear() {
    this.handleInputUpdate()
  }

  /** 敏感词 */
  async onKeyChange(e) {
    this.setData({
      isWarning: !!e.detail,
    })
    e.detail && this.getWarningHeight()
  }

  /** 获取warning-tip高度 */
  async getWarningHeight() {
    if (!this.data.warningHeight) {
      const dom = await getDom.call(this, '#warning-tip')
      // 如果没拿到高度就再获取一次
      if (!dom?.height) {
        setTimeout(async () => {
          const dom = await getDom.call(this, '#warning-tip')
          this.setData({
            warningHeight: dom?.height || 0,
          })
        }, 100)
        return
      }
      this.setData({
        warningHeight: dom?.height || 0,
      })
    }
  }

  /** 通过输入框组件更新数据 */
  handleInputUpdate() {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.updateValue()
      this.setData({ content: '', isWarning: false })
    })
  }

  /** 收起敏感词输入框键盘 */
  sensitiveHideKey() {
    wx.$.selectComponent.call(this, '#sensitive').then((sensitive) => {
      sensitive.setData({ inputFocus: false })
    })
  }
})
