<view class="body">
  <sensitive-textarea 
    style="{{(bottomHeight > 10 && isFocus) ? 'height: 25vh' : 'height: calc(100vh - 160rpx - 260rpx - 220rpx - ' + (isWarning ? warningHeight : 0 ) + 'px)'}}"
    class="sensitive-textarea"
    id="sensitive"
    placeholder="等待输入内容"
    value="{{content}}"
    hold-keyboard
    placeholder-class="placeholder"
    maxlength="{{-1}}"
    adjustPosition="{{false}}"
    disableDefaultPadding
    showConfirmBar="{{false}}"
    focus="{{!content}}"
    bind:focus="onFocus"
    bind:heightChange="onHideKey"
    bind:input="onInput"
    bind:keyChange="onKeyChange"
    bind:longtap="onLongTap"
    bind:heightChange="onHeightChange"
    />
  <warning-tip wx:if="{{isWarning && !isFocus}}" id="warning-tip" tip-class="warning-tip"  />
  <view class="footer-full"></view>
</view>
<view class="footer" style="{{(bottomHeight > 10 && isFocus) ? 'padding-bottom: 0' : ''}};bottom: {{bottomHeight || 0}}px">
  <view class="info-text">
    <view bind:tap="onClear" class="clear {{content.length < 1 && 'disabled'}}">清空内容</view>
    <view class="info-num">
      <view class="{{content.length > maxContent ? 'num-err' : 'num'}} {{content.length < 1 && 'num-gray'}}">
        {{content.length || 0}}
      </view>
      <view class="num-gray">/{{maxContent}}</view>
      <view class="btn btn-mini {{!(isFocus && bottomHeight > 10) && 'hidden'}}" bind:tap="onSubmit">
        {{footerText}}
      </view>
    </view>
  </view>
  <view class="btn {{(isFocus && bottomHeight > 10) && 'hidden'}}" bind:tap="onSubmit">{{footerText}}</view>
</view>
