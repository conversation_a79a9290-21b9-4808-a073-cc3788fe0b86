page{
  background-color: #fff;
}

.placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

.white-space {
  white-space: pre-wrap!important;
}

.item {
  .bottom-line();
  padding: 40rpx 0;
  .title {
    font-size: 30rpx;
    display: inline-flex;
    color: rgba(0, 0, 0, 0.65);
    padding-bottom: 16rpx;
  }
  .input {
    display: flex;
    align-items: center;
  }
  .input-text {
    flex: 1;
    font-size: 34rpx;
    .ellip(3);
  }
  .time-god {
    height: 48rpx;
    align-items: center;
    display: inline-flex;
    padding: 0 32rpx;
    color: rgba(0, 0, 0, 0.85);
    font-size: 34rpx;
  }

}

.desc {
  font-size: 26rpx;
  padding-top: 32rpx;
  padding-bottom: 16rpx;
  color: rgba(0, 0, 0, 0.25);
}

.img-box {
  padding: 32rpx 0;
}

.img-head {
  padding-bottom: 8rpx;
  color: rgba(0, 0, 0, 0.65);
}

.img-desc{
  font-size: 26rpx;
  padding-bottom: 24rpx;
  color: rgba(0, 0, 0, 0.45);
}

.footer {
  display: flex;
  margin: -20rpx;
}

.f-btn {
  .btn();
  margin: 10rpx !important;

  font-size: 34rpx;
  font-weight: bold;
  color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  text-align: center;
  background: #0092ff;
  border-radius: 12rpx;
  &:active {
    opacity: 0.8;
  }
}

.btn-save {
  flex: 1;
}
