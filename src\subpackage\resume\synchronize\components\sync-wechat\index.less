page {
  background-color: #fff;
}

.hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

.body {
  position: relative;
}

.we-info {
  line-height: 42rpx;
  padding-bottom: 20rpx;
}

.desc {
  font-size: 26rpx;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.25);
  display: flex;
}

.input-box {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  .bottom-line();
}

.input-right {
  display: flex;
  align-items: center;
  .input-clear{
    opacity: 0.7;
    padding: 6rpx 24rpx;
    display: inline-flex;
  }
}

.input {
  font-size: 30rpx;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  display: block;
  height: 112rpx;
  display: flex;
  align-items: center;
  flex: 1;
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
}
