
Page, .container {
    background: #fff;
    position: relative;
}

.container {
    padding: 0 32rpx;
    padding-bottom: 176rpx;
}
.header {
    padding: 32rpx 0;
}


.main {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.main-title, .paginator {
    font-size: 42rpx;
    line-height: 58rpx;
    color: rgba(0,0,0,0.85);
    font-weight: bold;
}

.primary {
    color: rgba(0,146,255,1);
}

.desc {
    font-size: 30rpx;
    font-weight: 400;
    color: rgba(0,0,0,.45);
    line-height: 42rpx;
    margin-top: 16rpx;
}


.fb-container {
    position: fixed;
    left: 32rpx;
    display: flex;
    flex-direction: column;
    bottom: 0;
}

.safe-padding-box {
    width: auto;
    background: transparent;
    min-height: 24rpx;
    height: env(safe-area-inset-bottom);
    height: content(safe-area-inset-bottom);
}

.btn-class {
    font-size: 34rpx;
    font-weight: bold;
} 

.customHeader {
    width: 100% !important;
}


