import { formatDatas, formatEditType, getHandlerData, transformTypeToResp } from './utils'
import { getResumeDetails } from '@/utils/helper/resume/index'
import { getResumeSecond } from '@/utils/helper/resume/utils'
import { isSubmit as validateEdu } from '../components/sync-edu/utils'
import { isSubmit as validateWork } from '../components/sync-work/utils'
import { isSubmit as validateProject } from '../components/sync-project/utils'
import { dealDialogRepByApi } from '@/utils/helper/dialog/index'

import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'

const menuBtnRect = getMenuButtonBoundingClientRect() || { width: 121 }

Page(class SynchronizeNew extends wx.$.Page {
  data = {
    /** 去掉input输入框的焦点，这个值都是去掉焦点 */
    closeFocus: false,
    /** 当前页面内容类型
     * introduce-个人优势 work-工作经历, project-项目经历, edu-教育经历 wx-微信
     */
    editType: 'work',
    /** 当前页面数据 */
    params: {},
    /** 同步类型
     * update (更新)
     * add (新增)
     * */
    synchroType: 'update',

    template: {},

    // data: cardTitle["work"],
    /** 当前同步简历步数 */
    step: 1,
    /** 总步数 */
    total: 0,
    /** 列表 */
    list: [],
    /** 在线简历数据 */
    resumeOnline: <any>{},
    /** 当前step的数据 */
    curSyncData: <any>{},
    /** 新增loading值 */
    loading: true,

    menuBtnRect,
  }

  uuid = ''

  /** 初始化 */
  async onLoad(options) {
    this.uuid = options.uuid || '1848268194602049591'
    const { resData } = wx.$.nav.getData()
    await Promise.all([
      /** 获取同步数据 */
      this.getSyncList(resData),
      /** 获取在线简历 */
      this.getResumeDetails()]).finally(() => {
      this.setData({
        loading: false,
      })
    })
    /** 判断简历是更新还是添加 */
    this.judgeSynchroType()
  }

  judgeSynchroType() {
    const { list, resumeOnline, step } = this.data
    console.log('judgeSynchroType', {
      list,
      resumeOnline,
    })

    const currentData = list[step - 1] || {}
    /** 获取resumeOnline字段 path */
    const propertyName = transformTypeToResp(currentData.type)

    let synchroType = 'update'

    let matched

    if (propertyName) {
      const data = wx.$.u.getObjVal(resumeOnline, propertyName)
      /** 个人优势，在线简历有该属性就是新增，没有就是更新 */
      if (currentData.type == 'introduce') {
        synchroType = data ? 'update' : 'add'
        matched = { introduce: data }
      } else {
        matched = data.find(item => item.uuid == currentData.uuid)
        synchroType = matched ? 'update' : 'add'
      }
      this.setData({
        synchroType,
        editType: currentData.type,
        template: getHandlerData(currentData.type),
        curSyncData: currentData.value,
        matched,
      })
    } else {
      this.setData({
        synchroType: 'add',
        editType: currentData.type,
        template: getHandlerData(currentData.type),
        curSyncData: currentData.value,
        matched,
      })
    }
  }

  /** 获取在线简历数据 */
  async getResumeDetails() {
    const [first = {}, second = {}] = await Promise.all([getResumeDetails(), getResumeSecond()])
    this.setData({
      resumeOnline: {
        ...first,
        ...second,
      },
    })
  }

  /** 获取同步简历数据 */
  async getSyncList(resData = null) {
    if (resData && wx.$.u.isArrayVal(resData.list)) {
      const newList = await formatDatas(resData.list)
      this.setData({
        list: newList,
        total: resData.list.length,
      })
      return
    }
    try {
      const response = await wx.$.javafetch['POST/resume/v3/attach/parseResume']({
        pickAttachUuid: this.uuid,
        /** mock */
      })
      /** 请求成功 */
      if (response && response.code == 0) {
        const { data: { list = [] } = {} } = response
        if (list.length == 0) {
          await wx.$.msg('此简历中暂无可同步的内容，建议您导入其他简历')
          wx.$.nav.back()
        }
        const newList = await formatDatas(list)
        console.log('------newList-------', JSON.stringify(newList))
        this.setData({
          list: newList,
          total: list.length,
        })
        /** 接口异常 */
      } else if (response && response.popup) {
        wx.$.showModal({
          ...response.popup,
          success: ({ jumpEventType }) => {
            if (jumpEventType == 3) {
              wx.$.nav.back()
            }
          },
        })
      } else if (response && response.message) {
        wx.$.msg(response.message)
      }
    } catch (e) {
      console.error('同步接口请求错误', e)
      await wx.$.msg('网络异常，请稍后再试')
      wx.$.nav.back()
    }
  }

  /** 点击跳过按钮 */
  onRightClick() {
    this.onBlurClear()
    this.onNextStep(true)
  }

  /** 点击返回按钮 */
  async onNavBack() {
    const popup = await dealDialogRepByApi('cvSyncExitDoubleCheck', { remainingStep: this.data.total - this.data.step + 1 })
    if (popup) {
      wx.$.showModal({
        ...popup,
        success: ({ jumpEventType }) => {
          if (jumpEventType == 4) {
            wx.$.nav.back()
          }
        },
      })
    }
  }

  /** 点击下一步 */
  async onNextStep(jumpNext?) {
    console.log(jumpNext)
    const { step, list, editType } = this.data
    const success = wx.$.u.getObjVal(jumpNext, 'detail.success')
    const msg = wx.$.u.getObjVal(jumpNext, 'detail.message')
    if (success || !jumpNext) {
      wx.$.collectEvent.event('resumeSynchronization', {
        operate: this.data.synchroType == 'add' ? '新增' : '更新',
        type: formatEditType(editType),
      })
    }
    if (step < list.length) {
      if (!jumpNext) {
        await wx.$.msg('内容已保存')
      } else if (msg) {
        await wx.$.msg(msg)
      }
      this.setData({ step: step + 1 })
      this.judgeSynchroType()
    } else {
      wx.$.collectEvent.event('resumeOnlineSuccess', {
        operate: '完成',
      })
      /** 更新下一步流程 */
      await wx.$.javafetch['POST/resume/v3/attach/updateFinishStatus']({ uuid: this.uuid })
      await wx.$.msg('简历同步完成')
      wx.$.nav.reLaunch('/subpackage/resume/publish/index')
    }
  }

  /** clear焦点事件input事件 */
  async onBlurClear() {
    const closeFocus = new Date().getTime()
    this.setData({ closeFocus })
    await wx.$.u.wait(10)
  }

  /** 编辑事件 */
  onEdit() {
    this.jumpEdit()
  }

  /** 跳转到编辑页 */
  jumpEdit(other = {}) {
    const { editType, curSyncData } = this.data
    const data = {
      editType,
      params: curSyncData,
      ...other,
    }

    if (editType === 'work' || editType === 'project') {
      // 处理时间的问题
      const keys = Object.keys(data)
      keys.forEach((key) => {
        const value = data[key]
        if (key.indexOf('startTime') > -1 && value) {
          // value : '2019-09-09' --> 2019.09
          data[key] = value.replace(/-/g, '.').slice(0, 5)
        }
        if (key.indexOf('endTime') > -1 && value) {
          data[key] = value.replace(/-/g, '.').slice(0, 5)
        }
      })
    }

    console.log('data', data)

    wx.$.nav.push(
      '/subpackage/resume/synchronize/edit/index',
      {},
      (params) => this.updateResult(params),
      {
        ...data,
      },
    )
  }

  /** 更新填充数据 */
  updateResult(params) {
    const { list, step } = this.data
    list[step - 1] = params
    console.log('updateResult', params)
    this.setData({ curSyncData: params })
  }

  /** 提交更新数据 */
  async submitUpdates() {
    await wx.$.u.waitAsync(this, this.submitUpdates, [], 500)
    const { editType } = this.data
    const { resumeUuid } = this.data.resumeOnline
    let dataParams: any = ''
    switch (editType) {
      case 'work':
        dataParams = validateWork(this.data.curSyncData)
        if (dataParams.error) {
          this.jumpEdit({ toast: dataParams.msg })
          return ''
        }
        return this.editWork(this.data.curSyncData)
      case 'project':
        dataParams = validateProject(this.data.curSyncData)
        if (dataParams.error) {
          this.jumpEdit({ toast: dataParams.msg })
          return ''
        }
        return this.editProject(this.data.curSyncData)
      case 'edu':
        dataParams = validateEdu(this.data.curSyncData)
        if (dataParams.error) {
          this.jumpEdit({ toast: dataParams.msg })
          return ''
        }
        return this.editEdu(this.data.curSyncData)
      case 'introduce':
        return this.editIntroduce({ ...this.data.curSyncData, resumeUuid })
      default:
        console.error('Updates')
        return ''
    }
    // if (editType == 'work' && validateWork(this.data.curSyncData)) {
    //   return this.editWork(this.data.curSyncData)
    // }
    // if (editType == 'project' && validateProject(this.data.curSyncData)) {
    //   return this.editProject(this.data.curSyncData)
    // }
    // if (editType == 'edu' && validateEdu(this.data.curSyncData)) {
    //   return this.editEdu(this.data.curSyncData)
    // }
    // if (editType == 'introduce') {
    //   return this.editIntroduce({ ...this.data.curSyncData, resumeUuid })
    // }
    // console.error('Updates')
    // return ''
  }

  /** 编辑项目经历 */
  editProject(params) {
    params.startTime = handlerSubTime(params.startTime)
    params.endTime = handlerSubTime(params.endTime)
    return wx.$.javafetch['POST/resume/v3/project/modify']({ ...params, isAttachSync: true }, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        return this.onNextStep()
      }
      wx.$.msg(res.message)
    }).catch(this.handleSubmitException)
  }

  /** 编辑教育经历 */
  editEdu(params) {
    const paramsObj = { ...params, isAttachSync: true }
    if (params.eduType == 0) {
      paramsObj.eduType = null
    }
    return wx.$.javafetch['POST/resume/v3/edu/modify'](paramsObj, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        return this.onNextStep()
      }
      // wx.$.msg(res.message)
      if ([28011001, 28014001, 28013002].includes(res.code)) {
        return this.onNextStep({ detail: { success: false, message: res.message } })
      }
    }).catch(this.handleSubmitException)
  }

  editWork({ jobTitle, jobPerformance, ...params }) {
    params.startTime = handlerSubTime(params.startTime)
    params.endTime = handlerSubTime(params.endTime)
    return wx.$.javafetch['POST/resume/v3/work/modify']({ ...params, isAttachSync: true }, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        return this.onNextStep()
      }
      // wx.$.msg(res.message)
      if ([28011001, 28014001, 28013002].includes(res.code)) {
        return this.onNextStep({ detail: { success: false, message: res.message } })
      }
    }).catch(this.handleSubmitException)
  }

  editIntroduce(params) {
    return wx.$.javafetch['POST/resume/v3/perfect/introduce'](params, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        return this.onNextStep()
      }
      // wx.$.msg(res.message)
      if ([28011001, 28014001, 28013002].includes(res.code)) {
        return this.onNextStep({ detail: { success: false, message: res.message } })
      }
    }).catch(this.handleSubmitException)
  }

  /** 接口请求错误 */
  handleSubmitException(response) {
    wx.hideLoading()
    if (response.code == 400) {
      response.message && wx.$.msg(response.message)
      this.jumpEdit()
      return
    }
    if ([28011001, 28014001, 28013002].includes(response.code)) {
      // return this.onNextStep()
      return this.onNextStep({ detail: { success: false, message: response.message } })
    }
    return wx.$.msg(response.message)
  }
})

export function handlerSubTime(time) {
  if (!time || time == '1990年以前' || time == '至今') {
    return null
  }
  const year = `${time}`.split('.')[0] // 取出小数点前的数（年份）
  let month = `${time}`.split('.')[1] || '01' // 取出小数点后的数（月份）
  // 确保月份是两位数
  if (month && month.length < 2) {
    month = `0${month}`
  }

  // 组合成所需的格式
  return `${year}-${month}`
}
