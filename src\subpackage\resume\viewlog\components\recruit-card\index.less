.recruit-card {
  position: relative;
  background-color: #fff;
  margin: 16rpx 0;
  margin-bottom: 20rpx;
  color: #333;
  font-size: 30rpx;
  overflow: hidden;

  .gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.no_margin_top {
  margin-top: 0rpx !important;
}

.no_margin_bottom {
  margin-bottom: 0rpx !important;
}

.content {
  padding: 24rpx 32rpx;
  box-sizing: border-box;

  .tag-icon {
    position: absolute;
    top: 0;
    left: 0;

    .icon {
      width: 80rpx;
      height: 80rpx;
    }
  }

  .card-stick {
    margin-left: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @primary-color;
    font-size: 28rpx;
    font-weight: bold;
    /* #ifdef weapp */
    line-height: 44rpx;
    /* #endif */

    .arrow-right {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .title {
    .textrow(2);
    overflow: hidden;
    word-break: break-all;
    font-size: 34rpx;
    font-weight: bold;
    line-height: 48rpx;
  }

  .user {
    display: flex;
    flex-direction: row;
    padding: 24rpx 0;
    position: relative;

    &.ac {
      align-items: center;
    }

    .cont {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .tag-common {
      padding: 0 12rpx;
      font-size: 24rpx;
      margin-right: 8rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      border-radius: 8rpx;
      font-weight: bold;
      flex: 0 0 auto;

      .tag-text {
        padding-left: 8rpx;
        box-sizing: border-box;
      }

      .tag {
        padding-left: 0rpx;
      }
    }

    .tag-suc {
      background: rgba(224, 242, 255, 1);
      color: @primary-color;
    }

    .tag-primary {
      background: rgba(224, 242, 255, 1);
      color: @primary-color;
    }

    .tag-recommond {
      background: #fff;
      border: 2rpx solid rgba(4, 181, 120, 1);
      color: rgba(4, 181, 120, 1);
      /* line-height: 44rpx;*/
    }

    .viewed-img {
      position: absolute;
      right: 0;
      bottom: -4rpx;

      .icon {
        width: 80rpx;
        height: 68rpx;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    box-sizing: border-box;
    width: 100%;
    line-height: 30rpx;
    margin-top: 4rpx;
    color: #808080;

    .address {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      line-height: 40rpx;
      color: rgba(0, 0, 0, 0.65);
      flex: 1;
      overflow: hidden;

      .address-tex {
        margin-left: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: rgba(0, 0, 0, 0.65);
      }

      .round-dot {
        width: 4rpx;
        height: 4rpx;
        border-radius: 50%;
        background: @text-third-color;
        margin: 0 13rpx;
        flex-shrink: 0;
      }
    }

    .date {
      margin-left: 32rpx;
      font-size: 26rpx;
      color: rgba(0, 0, 0, 0.45);
      text-align: right;
      word-break: break-all;
    }

    .viewed-oh {
      margin-left: 32rpx;
      font-size: 26rpx;
      color: #FF9800;
      text-align: right;
      word-break: break-all;
    }

    .check_comfirm {
      width: 192rpx;
      height: 56rpx;
      background-color: #FFF6EA;
      border-radius: 8rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #FF9800;
      margin-left: 14rpx;

      .check_img {
        margin-right: 8rpx;
        width: 40rpx;
        height: 40rpx;
      }

      .check_text {
        font-size: 24rpx;
        line-height: 32rpx;
        font-weight: bold;
      }
    }

    .boss-btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 188rpx;
      height: 56rpx;
      background: #e5f4ff;
      border-radius: 8rpx;
      color: #0092ff;
      font-size: 28rpx;
      flex-shrink: 0;
      margin-left: 24rpx;
      padding-right: 16rpx;
      box-sizing: border-box;

      .icon {
        width: 48rpx;
        padding-left: 8rpx;
      }

      .text {
        font-weight: bold;
      }
    }
  }
}

.cancel-coll {
  width: 100vw;
  font-size: 28rpx;
  color: @primary-color;
  line-height: 80rpx;
  text-align: right;
  background: @white-color;
  border-top: 2rpx solid #eff1f6;
  padding: 0 @padding;
}

.contact {
  display: flex;
  justify-content: space-between;
}

.contact-time {
  color: @text-secondary-color;
  font-size: 24rpx;
}

.recruit-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  height: 48rpx;
  overflow: hidden;

  &.is-viewed {
    margin-right: 104rpx;
  }

  .r-tag {
    flex-shrink: 0;
    padding: 0 12rpx;
    font-size: 26rpx;
    margin-right: 8rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    border-radius: 8rpx;
    background: #F5F6FAFF;
    color: rgba(0, 0, 0, 0.651);

    &:last-child {
      margin-right: 0;
    }
  }

  .hightLightTag {
    background: #FFF4E5;
    color: #FF9800;
    font-weight: bold;
  }
}

.address-box {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.img {
  width: 102rpx;
  height: 40rpx;
  float: left;
  display: inline;
  position: relative;
  top: 4rpx;
  left: 0;
  margin-right: 8rpx;
}
