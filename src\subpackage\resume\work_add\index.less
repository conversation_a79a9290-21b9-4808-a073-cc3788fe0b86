page{
  background-color: #fff;
}

.body{
  padding: 0 32rpx;
}

.placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
}

.item{
  .bottom-line();
  padding: 40rpx 0;
  .title {
    font-size: 30rpx;
    display: inline-flex;
    padding-bottom: 16rpx;
    color: rgba(0, 0, 0, 0.65);
  }
  .time {
    display: flex;
    align-items: center;
  }
  .time-text{
    flex: 1;
    font-size: 34rpx;
  }
  .time-god{
    height: 48rpx;
    align-items: center;
    display: inline-flex;
    padding: 0 32rpx;
    color: rgba(0, 0, 0, 0.85);
    font-size: 34rpx;
  }
}

.desc {
  font-size: 26rpx;
  padding-top: 32rpx;
  padding-bottom: 16rpx;
  color: rgba(0, 0, 0, 0.25);
}

.footer {
  display: flex;
  margin: -20rpx;
}

.f-btn {
  .btn();
  margin: 10rpx !important;

  font-size: 34rpx;
  font-weight: bold;
  color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  text-align: center;
  background: #0092ff;
  border-radius: 12rpx;
  &:active {
    opacity: 0.8;
  }
}

.btn-save {
  flex: 1;
}

.btn-del {
  width: 220rpx !important;
  background: #f5f6fa;
  color:rgba(0, 0, 0, 0.65);
}

.company-shield {
  display: flex;
  align-items: center;
  padding: 40rpx 0;
  .company-label {
    line-height: 44rpx;
    color: rgba(0, 0, 0, 0.85);
    flex: 1;
  }
}
