/*
 * @Date: 2024-05-14 19:02:55
 * @Description: 添加/编辑工作经历
 */

import { store } from '@/store/index'
import { dealDialogShow } from '@/utils/helper/dialog/index'
import { refreshMyInfo } from '@/utils/helper/resume/index'
import { isEqualObj } from '@/utils/tools/common/index'
import { getInitFormData, getParams, handlerSubTime, isSubmit } from './utils'
import { updateResume } from '../utils/index'

const defFormData: any = {
  /** 公司id */
  companyId: null,
  /** 公司名称 */
  companyName: '',
  /** 开始时间 */
  startTime: '',
  /** 结束时间 */
  endTime: '至今',
  /** occId: 职位名称 */
  occId: '',
  /** 工作内容 */
  jobContent: '',
  /** 工作业绩 */
  jobPerformance: '',
  /** 所属部门 */
  departmentName: '',
  /** 是否隐藏 */
  isHide: true,
}
Page(class extends wx.$.Page {
  waterMaskFiles = { images: [] }

  /** 首次提交的数据 */
  oldFormData = {}

  data = {
    title: '',
    /** 时间选择器是否显示 */
    visible: false,
    /** picker的value */
    pickerValue: '',
    /** 时间类型, start: 开始时间， end: 结束时间 */
    type: 'start',
    /** 工作经历数据 */
    formData: {} as typeof defFormData,
    /** 工种数据 */
    classifyData: {},
    /** uuid */
    uuid: '',
    /** 公司信息 */
    companyInfo: {
      code: '',
      /**
       * 是否显示屏蔽开关
       */
      isShowShield: false,
      isShield: false,
      name: '',
    },
  }

  /** 初始化 */
  init() {
    const params = wx.$.nav.getData()
    if (params && params.uuid) {
      // /resume/v3/work/query
      // 编辑时需要传入整个对象
      const { formData, companyInfo, classifyData } = getInitFormData.call(this, params)
      this.setData({
        formData,
        companyInfo,
        title: '编辑工作经历',
        classifyData,
        uuid: params.uuid,
      })
      this.oldFormData = wx.$.u.deepClone(formData)
    } else {
      const formData = getParams(defFormData)
      this.oldFormData = { ...formData }
      this.setData({
        formData,
        title: '添加工作经历',
        uuid: '',
      })
    }
  }

  onLoad() {
    this.init()
    updateResume()
  }

  /** 点击保存按钮 */
  async onSubmit() {
    await wx.$.u.waitAsync(this, this.onSubmit, [], 1100, null, 'loading')
    const { formData } = this.data
    const params = isSubmit(formData)

    if (!params) return
    if (isEqualObj(params, this.oldFormData)) {
      wx.$.r.back()
      return
    }

    wx.$.loading('保存中...')
    params.startTime = handlerSubTime(params.startTime)
    params.endTime = handlerSubTime(params.endTime)
    if (params.uuid) { // 编辑
      await this.editProject(params)
      return
    }
    // 添加
    await this.addProject(params)
    wx.hideLoading()
  }

  /** 添加工作经历 */
  addProject(params) {
    /* {"code":28019002,"message":"屏蔽公司成功","askId":"a8c3806c08da559d","data":null,"error":false} */
    return wx.$.javafetch['POST/resume/v3/work/add'](params, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      const { code, error, message } = res
      /** 28019002 */
      if (!error) {
        const msg = code == 0 ? '保存成功' : message
        this.submitSuccess(msg)
      } else {
        wx.$.msg(res.message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  /** 编辑工作经历 */
  editProject(params) {
    return wx.$.javafetch['POST/resume/v3/work/modify'](params, { isWeToken: false }).then((res) => {
      wx.hideLoading()
      const { code, error, message } = res
      /** 28019002 */
      if (!error) {
        const msg = code == 0 ? '保存成功' : message
        this.submitSuccess(msg)
      } else {
        wx.$.msg(res.message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  /** 删除工作经历 loadingDel */
  async onDelete() {
    await wx.$.u.waitAsync(this, this.onDelete, [], 0, null, 'loadingDel')
    const { uuid } = this.data
    let dialogRes = await dealDialogShow({
      dialogIdentify: 'gongzuojingli_querenshanchu',
    })
    const { itemClass, btnIndex } = dialogRes || { btnIndex: 0, itemClass: 'cancel' }
    if (itemClass == 'none') {
      dialogRes = await wx.$.confirm({
        content: '删除这条工作经历吗？',
        confirmText: '删除',
      }).then(() => {
        return { btnIndex: 1 }
      }).catch(() => {
        return { btnIndex: 0 }
      })
    }
    if (btnIndex == 0) {
      return
    }
    wx.$.loading('删除中...')
    wx.$.javafetch['POST/resume/v3/work/delete']({
      uuid,
    }).then((res) => {
      wx.hideLoading()
      if (res.code == 0) {
        this.submitSuccess('删除成功')
      } else {
        wx.$.msg(res.message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  /** 提交成功的处理方法 */
  submitSuccess(msg = '保存成功') {
    refreshMyInfo()
    wx.$.msg(msg, 1000, true).then(() => {
      wx.$.r.back()
    })
  }

  /** 显示时间选择器 */
  onShowTime(e) {
    const { type } = e.currentTarget.dataset
    const { startTime, endTime } = this.data.formData as any
    const time = type == 'start' ? startTime : endTime
    let pickerValue = []
    if (time) {
      pickerValue = time.split('.')
    }

    this.setData({
      type,
      visible: true,
      pickerValue,
    })
  }

  /** 返回逻辑 */
  async onBack() {
    const { formData } = this.data
    const params = getParams(formData)
    if (isEqualObj(params, this.oldFormData)) {
      wx.$.r.back()
      return
    }

    dealDialogShow({
      dialogIdentify: 'jlwbcfhts',
    }).then(res => {
      const { itemClass, btnIndex } = res || { btnIndex: 0, itemClass: 'cancel' }
      if (itemClass == 'none') {
        wx.$.confirm({
          content: '内容尚未保存,确定退出?',
        }).then(() => {
          wx.$.r.back()
        }).catch(() => {})
        return
      }
      if (btnIndex == 0) {
        return
      }
      wx.$.r.back()
    })
  }

  /** 点击时间选择器确定按钮 */
  onChangeTime({ detail }) {
    const { type } = this.data
    const { valueStr } = detail
    if (type == 'start') {
      this.setData({
        'formData.startTime': valueStr,
      })
    } else {
      this.setData({
        'formData.endTime': valueStr,
      })
    }
    this.onCancelTime()
  }

  /** 设置内容-多行文本框 */
  onEdit(e) {
    const { formData } = this.data
    const { name, type } = e.currentTarget.dataset
    wx.$.nav.push(
      '/subpackage/resume/edit_textarea/index',
      { type },
      (data) => {
        formData[name] = data.content
        this.setData({ formData })
      },
      { content: formData[name] || '' },
    )
  }

  /** 设置内容-单行文本框 */
  onEditSingle(e) {
    const { formData } = this.data
    const { name, type } = e.currentTarget.dataset
    wx.$.nav.push(
      '/subpackage/resume/edit_input/index',
      {
        type,
        sameContentVerify: name !== 'companyName',
      },
      (data) => {
        if (name === 'companyName') {
          // 公司名称firm-shield
          // formData[name] = data.content
          // 公司名称修改屏蔽开关默认开启状态
          // 编辑模式，公司名称与编辑前一致，对该公司隐藏我的信息开关保持原设置
          if (data.content !== formData[name]) {
            // 输入内容与表单内容不一致重置开关默认开启状态
            formData.isHide = true
            formData.companyId = data.code || null
            this.setData({ companyInfo: data })
          } else {
            formData.companyId = data.code || formData.companyId || null
            this.setData({
              companyInfo: { ...this.data.companyInfo, ...data }
            })
          }
        }
        formData[name] = data.content
        this.setData({ formData })
      },
      { content: formData[name] || '' },
    )
  }

  /** 设置职位名称 */
  onEditJob() {
    const params = {
      isSelectToBack: true,
      maxSelectNum: 1,
      title: '选择期望职位',
      value: [],
    }
    // this.setData({ _isJumpPage: true, _jumpPageType: 'bottomFullScreen' })
    wx.$.nav.push(
      '/subpackage/classify/bottom-full-screen/index',
      {},
      (data) => {
        const classifyData = data.classifyArr[0]
        this.setData({
          classifyData,
          'formData.occId': classifyData.id,
        })
      },
      params,
    )
  }

  /** 点击时间选择器取消按钮 */
  onCancelTime() {
    this.setData({ visible: false })
  }

  /** 对公司隐藏我的信息 */
  async onSwitchShield(e) {
    const { formData } = this.data
    formData.isHide = !formData.isHide
    this.setData({ formData })
  }
})
