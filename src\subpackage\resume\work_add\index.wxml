<custom-header title="{{title}}" customBack border bind:back="onBack"/>

<view class="body">
  <form-item
    title="公司名称"
    bind:click="onEditSingle"
    value="{{formData.companyName}}"
    data-name="companyName"
    data-type="workComName"
  />
  <view class="item">
    <view class="title">在职时间</view>
    <view class="time">
      <view
        data-type="start"
        bind:tap="onShowTime"
        class="time-text {{formData.startTime ? '' : 'placeholder'}}"
      >
        {{formData.startTime ? formData.startTime : '开始时间'}}
      </view>
      <view class="time-god">—</view>
      <view
        data-type="end"
        bind:tap="onShowTime"
        class="time-text {{formData.endTime ? '' : 'placeholder'}}"
      >
        {{formData.endTime ? formData.endTime : '结束时间'}}
      </view>
    </view>
  </view>

  <form-item
    title="职位名称"
    bind:click="onEditJob"
    value="{{classifyData.name}}"
    data-name="occId"
    placeholder="请选择"
  />

  <form-item
    bind:click="onEdit"
    isEllip
    title="工作内容"
    value="{{formData.jobContent}}"
    data-name="jobContent"
    data-type="workCont"
  />

  <view class="desc">以下为选填项</view>

  <form-item
    bind:click="onEdit"
    title="工作业绩"
    value="{{formData.jobPerformance}}"
    data-name="jobPerformance"
    data-type="workKPI"
    placeholder="选填 请输入"
  />

  <form-item
    bind:click="onEditSingle"
    title="所属部门"
    value="{{formData.departmentName}}"
    data-type="workDepName"
    data-name="departmentName"
    placeholder="选填 请输入"
  />

  <!-- 对公司隐藏该信息 - companyInfo -->
  <block wx:if="{{formData.companyName}}" >
    <view class="company-shield">
      <view class="company-label">对该公司隐藏我的信息</view>
      <m-switch bind:click="onSwitchShield" active="{{formData.isHide || false}}"/>
    </view>
  </block>
</view>

<m-button-footer isCustom>
  <view class="footer">
    <button wx:if="{{!!uuid}}" bind:tap="onDelete" loading="{{loadingDel}}" class="f-btn btn-del">删除</button>
    <button bind:tap="onSubmit" loading="{{loading}}" class="f-btn btn-save">保存</button>
  </view>
</m-button-footer>

<!-- 时间选择器 -->
<project-time-picker
  type="{{type}}"
  visible="{{visible}}"
  value="{{pickerValue}}"
  bind:submit="onChangeTime"
  bind:cancel="onCancelTime"
/>
