.content {
  padding: 32rpx 24rpx 0rpx 24rpx;
}

.hide {
  display: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 8rpx;
  padding-right: 8rpx;
  padding-bottom: 24rpx;

  .title {
    font-weight: bold;
    font-size: 34rpx;
    color: rgba(0, 0, 0, 0.85);
  }

  .desc {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.desc-close {
  margin-right: 8rpx;
}

.desc-text {
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
}

.list {
  display: flex;
  flex-wrap: wrap;
}

.item {
  padding: 8rpx;
  overflow: hidden;

  .tag {
    font-size: 30rpx;
    padding: 15rpx 24rpx;
    color: rgba(0, 0, 0, 0.85);
    border-radius: 16rpx;
    background: #f5f6fa;
    .ellip;
    .active;
  }
}
