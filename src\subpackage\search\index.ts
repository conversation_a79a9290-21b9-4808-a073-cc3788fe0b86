/*
 * @Date: 2022-03-02 13:45:44
 * @Description: 搜索页
 * @page-query: origin=recruit|resume
 *  origin：recruit代表搜索招工，resume代表搜索找活
 *  bubble：1.是否是招工引导搜索跳转过来的
 */

import { MapStateToData, connectPage, dispatch, actions, store } from '@/store/index'
import { uploadCommonSearchWordsStatisticsData } from '@/utils/helper/common/index'
import { tools, helper } from '@/utils/index'
import { getHotSearch, getLabelList, clearSearchHistory } from './utils'
import { getSystemInfoSync, throttle } from '@/utils/tools/common/index'
import { saveSearchHistory } from '@/utils/helper/list/index'
import resource from '@/components/behaviors/resource'

const mapStateToData: MapStateToData = (state) => {
  const { classify, storage } = state
  const { common } = storage
  const { perRecommendationSet } = common || {}
  // 招工选择的城市
  const recruitArea = {
    label: helper.location.getLocalCity('recruit')?.name,
    value: helper.location.getLocalCity('recruit')?.id,
  }

  // 找活选择的城市
  const resumeArea = {
    label: helper.location.getLocalCity('resume')?.name,
    value: helper.location.getLocalCity('resume')?.id,
  }
  return {
    // 招工选择的城市
    recruitArea,
    // 找活选择的城市
    resumeArea,
    // 列表可选工种数量
    projectSearchOccCnt: classify.classifyConfig.projectSearchOccCnt,
    perRecommendationSet: perRecommendationSet || {},
  }
}

Page(
  connectPage(mapStateToData)({
    behaviors: [resource],
    /** 页面的初始数据 */
    data: {
      top: tools.common.getHeaderHeight('96rpx', true),
      // 显示隐藏弹框 area | classify | label
      visible: '',
      // 页面路由参数
      query: {
        /** 页面来源：recruit，resume */
        origin: 'recruit',
        /**
         * 搜索触发位置。
         * 0-【搜索中间页】搜索框或者【列表页面相关推荐】关键词，
         * 1-招工详情搜索按钮，
         * 2-招工详情点击【搜一搜】推荐的关键字。
         * 3 - 首页头部搜索 , 搜索结果页；这时，路由上 searchKeyWord 带了底纹词 关键词。搜索的时候需要用到
         * 默认值：0。
         */
        type: '0',
        job_id: '', // 招工详情带来的id
        search_word: '', // 搜索词用于请求预置词的接口
        // （1-招工大列表顶部搜索框、2-招工详情页顶部搜索按钮、3-搜索结果页）。埋点使用
        enter_id: '1',
        fromPage: '', // 从哪个页面跳转过来的 (recruitIndex: 招工首页)
        bubble: '', // 是否是从招工引导搜索跳转过来的 (recruitIndex: 招工首页)
        searchKeyWord: '', // 根牛人首页的搜索关键词 来填充文案，占位符显示
        /** 底纹词， 没有则为 ‘’ */
        word: '',
      },
      /** 页面来源：recruit，resume */
      origin: 'recruit',
      // 顶部高度
      headerTop: '200rpx',
      // 热门搜索数据列表
      hotSearchList: [],
      // 搜索关键字
      keywords: '',
      // 输入框的内容(实时更新)
      value: '',
      // 搜索的标签列表
      labelList: [],
      // 搜索框的预置词
      beforehandWords: '',
      ENV_SUB,
      searchHistory: [],
      /** 键盘的焦点状态 */
      keyBoardFocus: true,
      sysInfo: getSystemInfoSync(),
      /** 资源位标识对应的数据key */
      resourceKeys: {
        banner_search_middle: 'privateDomainBanner',
      },
      privateDomainBanner: {},
    },

    /** 生命周期函数--监听页面加载 */
    onLoad(options) {
      const headerTop = tools.common.getHeaderHeight('84rpx', true)
      const query = { ...options, origin: options.origin || 'recruit' }
      // 转码
      query.searchKeyWord = decodeURIComponent(query.searchKeyWord)
      // 如果是搜索结果页面回到 当前页面。并且是 底纹词 路径 类型的
      if (query.fromPage === 'searchResults' && query.type == '3') {
        this.setData({ value: query.searchKeyWord })
        // 请求推荐关键词
        this.onChange({ detail: query.searchKeyWord })
      }

      // 回显到输入框里面的数据
      this.setData({ query, headerTop, origin: query.origin })
      if (query?.type == 1) {
        this.initBeforehandWords()
      }
    },
    /** 生命周期函数--监听页面显示 */
    onShow() {
      const { searchHistory } = store.getState().storage
      let focus = this.data.keyBoardFocus
      if (this.data.query.type == '3') {
        focus = true
      }
      this.setData({ searchHistory, keyBoardFocus: focus })
      // 获取热门搜索列表
      const paramsObj = {
        historyData: searchHistory,
        origin: this.data.query.origin,
      }
      const { perRecommendationSet } = this.data
      if (perRecommendationSet.search && ENV_SUB !== 'zyqg') {
        getHotSearch(paramsObj)
          .then((list) => {
            this.setData({ hotSearchList: list })
          })
          .then(() => {
            // 搜索中间页的搜索历史和猜你喜欢--曝光埋点
            this.eventTrackingSearchWords()
          })
      } else {
        // 搜索中间页的搜索历史和猜你喜欢--曝光埋点
        this.eventTrackingSearchWords()
      }
      // 搜索中间页的曝光上报
      this.eventTracking()
    },

    /** 埋点
     * source_id = '1' 招工 /'2'找活
     */
    eventTracking() {
      const { query } = this.data
      const { enter_id } = query || {}
      if (!enter_id) {
        return
      }
      const source_id = query.origin == 'recruit' ? '1' : '2'
      const reportParams = {
        enter_id,
        source_id,
      }
      wx.$.collectEvent.event('searchPageExposure', reportParams)
    },

    /**
     * 搜索历史和猜你喜欢--曝光埋点
     * （1-搜索中间页搜索历史、2-搜索中间页猜你喜欢、3-招工详情页搜一搜标签、4-招工大列表RS词、5-搜索结果页GS词）
     */
    eventTrackingSearchWords() {
      const { searchHistory, hotSearchList } = this.data
      let type = '1'
      if (this.data.query.origin == 'recruit') {
        if (searchHistory.recruit?.length > 0) {
          uploadCommonSearchWordsStatisticsData('searchLabelsExposure', type, searchHistory.recruit)
        }
        if (hotSearchList?.length > 0) {
          type = '2'
          uploadCommonSearchWordsStatisticsData('searchLabelsExposure', type, hotSearchList)
        }
      }
    },

    /** 开始搜索 */
    onSearch(e) {
      const { query } = this.data
      const { origin, fromPage, searchKeyWord, type, word } = query || {}
      const keywords = e.detail?.item || e.detail
      // 招工板块, 牛人端
      if (origin == 'recruit') {
        // 底纹词是否无效。如果是 搜索职位 则是无效底纹词
        const notPatternWords = word === '搜索职位'
        // 是不是无效的关键词
        const notKeywords = !keywords || keywords?.trim() == ''

        if (type === '3') {
          wx.$.collectEvent.event('searchInput', {
            keywords: notPatternWords ? '' : word,
            search_source_id: 2,
            search_source: 2,
          })
        }
        /**
         * 1.搜索框没有自定义的内容 2.底纹词不是 搜索职位 3. 从首页的搜索框点击过来的
         */
        if (notKeywords && notPatternWords && type === '3') {
          wx.$.msg('请输入您想搜索的职位')
          return
        }

        if (type == '3' && notKeywords && keywords) {
          wx.$.msg('搜索内容不能为空')
          return
        }

        // 搜索框的预置词，type !== 3 的时候才会执行逻辑 . 有底纹词在的时候，就不走这个判断
        if (notKeywords && !this.data.beforehandWords && type != '3') {
          wx.$.msg('搜索内容不能为空')
          return
        }
        const keywords_source = wx.$.u.getObjVal(e, 'currentTarget.dataset.source', '5')
        // 最后带给 搜索结果页的搜索词
        let finally_keywords = keywords
        // 底纹词 走的逻辑
        if (notKeywords && !notPatternWords && type === '3') {
          finally_keywords = word
        }

        // 判断预置词
        if (notKeywords && type != '3') {
          finally_keywords = this.data.beforehandWords
        }
        saveSearchHistory(origin, finally_keywords.trim())

        this.setData({
          keywords: type == '3' ? '' : (finally_keywords || this.data.keywords),
          value: type == '3' ? '' : (finally_keywords || '').trim(),
          visible: '',
          query: { ...query, enter_id: 3 },
        })
        // 表示带过去的搜索词 是底纹词
        const patternWords = searchKeyWord === finally_keywords && type === '3'
        // 格式化输入框里面的特殊字符，不然页面路径上带不了这些特殊字符
        const codeString = encodeURIComponent(`${finally_keywords.trim()}`)
        wx.$.r.push({
          path: '/subpackage/recruit/listSearchResultsPage/index?key',
          query: {
            keywords: type === '3' ? codeString : finally_keywords,
            enter_id: patternWords ? '9' : '1',
            patternWords,
            fromPage,
            keywords_source: patternWords ? '7' : keywords_source,
            type,
            word: type === '3' ? (query.word || query.searchKeyWord) : '',
          },
        })
      } else {
        // 找活板块
        if (!keywords || keywords?.trim() == '') {
          wx.$.msg('搜索内容不能为空')
          return
        }
        saveSearchHistory(origin, keywords)
        this.setData({ keywords, value: keywords, visible: '', query: { ...query, enter_id: 3 } })
        wx.$.r.push({ path: '/subpackage/resume/listSearchResultsPage/index', query: { keywords, enter_id: '5' } })
      }
    },

    onBlur: wx.$.u.debounce(function () {
      if (this.data.query.type == '3') {
        this.setData({ keyBoardFocus: false })
      }
    }, 200),

    /** 页面隐藏 */
    onHide() {
      this.setData({ visible: '', keyBoardFocus: false })

      this.clearBeforehandWords()
    },

    /** 清除预置词 */
    clearBeforehandWords() {
      this.setData({ beforehandWords: '' })
    },

    /** 搜索框输入事件 */
    onChange: wx.$.u.debounce(function (e) {
      this.clearBeforehandWords()
      const keywords = (`${e.detail}` || '').trim().replace(/\n/g, '')
      const area_id = this.data.recruitArea?.value
      if (keywords) {
        const { origin } = this.data.query
        if (origin == 'recruit') {
          wx.$.collectEvent.event('searchInput', { keywords })
        }
        // 搜索匹配的标签
        getLabelList(area_id, keywords).then((labelList) => {
          this.setData({
            keywords,
            labelList,
            visible: labelList && labelList.length > 0 ? 'label' : '',
          })
        })
      } else {
        this.setData({ keywords, visible: '' })
      }
    }, 300),

    /** 在有预置词的情况下，聚焦--事件 */
    onFucus: throttle(function () {
      if (this.data.query.type == '3') {
        this.setData({ keyBoardFocus: true })
      }
      this.clearBeforehandWords()
    }),

    /** 清空历史搜索记录 */
    onClearHistory() {
      const { origin } = this.data.query
      clearSearchHistory(origin)
      const { searchHistory } = store.getState().storage
      this.setData({ searchHistory })
    },

    /** 搜索框清空内容事件 */
    onClear() {
      this.setData({ keywords: '', visible: '' })
      if (this.data.query.type == '3') {
        this.setData({ keyBoardFocus: true })
      }
    },
    // 初始化预置词
    async initBeforehandWords() {
      const { query } = this.data
      if (query?.search_word != '') {
        const outer: any = await dispatch(actions.searchActions.rqSearchBeforehandWords(query?.search_word, query?.type, query?.job_id, []))
        this.setData({ beforehandWords: outer?.word })
      }
    },
  }),
)
