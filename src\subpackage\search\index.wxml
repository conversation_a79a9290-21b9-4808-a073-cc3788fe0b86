<page-meta page-style="overflow: {{visible ==='label' ? 'hidden' : 'visible'}}" />

<view class="search-header" >
  <!-- 标题 -->
  <custom-header title="{{query.origin == 'recruit' ? '搜索职位' : '搜索简历'}}" />
  <!-- 搜索框。老板端展示这个 -->
  <view wx:if="{{ query.type != '3' }}">
    <m-search-input
      focus="{{true}}"
      value="{{value}}"
      defaultValue="{{keywords}}"
      placeholder="{{query.origin == 'recruit' ?  beforehandWords ? beforehandWords : '搜索您想找的职位' : '搜索您想要的牛人'}}"
      cancelText="搜索"
      bind:change="onChange"
      bind:clear="onClear"
      bind:cancel="onSearch"
      bind:confirm="onSearch"
      bind:focus="onFucus"
      custom-search-input="custom-search-input"
      custom-input="custom-input"
      custom-cancel="custom-cancel"
      closeIcon="yp-shanchu"
      custom-close-icon="custom-close-icon"
      data-source="5"
      placeholderStyle="font-size: 30rpx"
    >
      <!-- <view slot="left" bind:tap="onTogglePicker" data-name="area" class="city {{visible == 'area' ? 'active' : ''}}">
        <text class="search-text">{{query.origin == 'recruit' ? recruitArea.label : resumeArea.label}}</text>
        <icon-font type="{{visible == 'area' ? 'yp-icon_tab_sx_hig' : 'yp-icon_tab_sx_nor'}}" custom-class="city-icon" size="24rpx" color="rgba(0, 0, 0, 0.85)"></icon-font>
      </view> -->
    </m-search-input>
  </view>

  <!-- 牛人端展示这个样式 -->
  <search-input
    wx:else
    searchBoxStyle="background: #f5f7fcff"
    placeholder="{{ query.type == '3' ? (query.word || query.searchKeyWord) : '搜索您想找的职位' }}"
    focus="{{sysInfo.mid == 'pc' ? false : keyBoardFocus}}"
    bind:change="onChange"
    confirm-type="search"
    value="{{value}}"
    defaultValue="{{keywords}}"
    bind:clear="onClear"
    bind:confirm="onSearch"
    bind:focus="onFucus"
    bind:search="onSearch"
    bind:blur="onBlur"
    clearable="{{ keyBoardFocus }}"
    wrapStyle="margin-top: 14rpx"
  />


</view>

<view>
  <search-content
    title="历史搜索"
    desc="清空历史"
    type='1'
    list="{{searchHistory[origin]}}"
    keywords="{{keywords}}"
    origin="{{origin}}"
    data-source="4"
    bind:desc="onClearHistory"
    bind:click="onSearch"
  />
  <search-content
    wx:if="{{perRecommendationSet.search && ENV_SUB !== 'zyqg'}}"
    type='2'
    title="搜索发现"
    data-source="0"
    list="{{hotSearchList}}"
    keywords="{{keywords}}"
    origin="{{origin}}"
    bind:click="onSearch"
  />
  <!-- 私域引流图片组件 -->
  <private-domain-banner bannerData="{{privateDomainBanner}}" isShowAdditional="{{visible !== 'label'}}" />

</view>

<!-- 搜索标签内容 -->
<search-label
  wx:if="{{visible == 'label'}}"
  top="{{headerTop}}"
  list="{{labelList}}"
  keywords="{{keywords}}"
  origin="{{origin}}"
  bind:click="onSearch"
  data-source="1"
/>