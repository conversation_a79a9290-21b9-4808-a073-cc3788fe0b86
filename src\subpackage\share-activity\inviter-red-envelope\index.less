page {
  background-color: rgba(255, 235, 224, 1); /* 设置背景颜色以覆盖滚动条 */
}

.page-wrap {
  min-height: 100vh;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: top;
  background-size: 100vw 1240rpx;
  background-image: url('https://cdn.yupaowang.com/yupao_mini/share_red_envelope/yp_mini_red_bg1.png');
}


.title {
  width: 100%;
  height: 204rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 28rpx 0 48rpx 0;
  position: relative;
  .title_img{
    height: 100%;
    width: 568rpx;
  }
}

.red-container {
  width: 632rpx;
  height: 664rpx;
  margin: 0px 77rpx 38rpx 80rpx;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 651rpx 664rpx;
  background-image: url('https://cdn.yupaowang.com/yupao_app/yp_mini_red_new_share_bg.png');

  
  .content {
    width: 556rpx;
    margin-left: 15.6rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    .title_tip_img {
      width: 388rpx;
      height: 82rpx;
      position: absolute;
      left: 15%;
      top: -7.2rpx
    }
  }
  
  .success-message {
    width: 436rpx !important;
    height: 66rpx !important;
    border-radius: 32rpx;
    background-color:rgba(233, 52, 57, 0.3);
    color: rgba(255, 185, 187, 1);
    font-size: 24rpx;
    margin-bottom: 32rpx;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 16rpx 32rpx;
    margin-top: 117.2rpx;
    .message {
      width: 100% !important;
      height: 34rpx !important;
      text-align: center;
    }
  }
  
  .red-envelopes {
    width: fit-content;
    height: 144rpx;
    font-weight: bold;
    text-align: center;
    font-size: 32rpx;
    color: rgba(254, 249, 229, 1);
    position: absolute;
    left: 124rpx;
    top: 200rpx;
    z-index: 2;
    .amount_num {
      font-size: 144rpx;
    }
    .amount {
      font-size: 50rpx;
      color: rgba(254, 249, 229, 1);
    }
    .des_num {
      color: rgba(255, 227, 199, 1);
      font-size: 24rpx;
      margin-right: 10rpx;
    }
    .gradient-text {
      background-image:linear-gradient(180deg, rgba(255, 251, 249, 1) 0%, rgba(255, 223, 191, 1) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .float-tag {
      width: 130rpx;
      height: 52rpx;
      background-color: #ffe3d4;
      border-radius: 16rpx 16rpx 16rpx 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: -99rpx;
      top: -1rpx;
      z-index: 3;
      .envelope-icon {
        width: 32rpx;
        height: 32rpx;
      }
      .with_pay {
        color: rgba(190, 33, 0, 1);
        font-size: 26rpx;
        margin-left: 4rpx;
      }
    }
  }
  
  .invite-button {
    margin-top: 282rpx;
    width: 100%;
    height: 88rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;


    .red-float {
      position: absolute;
      width: 164rpx;
      height: 84rpx;
      right: 71rpx;
      top: -30rpx;
    }
  
    .button {
      width: 400rpx;
      height: 88rpx;
      font-size: 34rpx;
      color: rgba(176, 12, 16, 1);
      background: linear-gradient(180deg, rgba(255, 236, 200, 1) 0%, rgba(255, 200, 140, 1) 100%);
      border-radius:64rpx;
      box-shadow: 0px 4rpx 4rpx rgba(232, 31, 37, 1);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      .we-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }
    }
    .disable-btn {
      background: rgba(255, 236, 200, 1) !important;
      color: rgba(226, 153, 132, 1) !important;
    }
  }
}


.card-wrap {
  width: 100%;
  padding: 0 24rpx 48rpx 24rpx;
  .card {
    width: 702rpx;
    height: auto;
    background-color: #fff;
    border-radius: 32rpx;
    margin-top: 40rpx;
    display: flex;
    flex-direction: column;

    .text-box {
      width: 100%;
      height: auto;
      padding: 24rpx 32rpx;
      .line-text {
        font-size: 30rpx;
        color: rgba(126, 70, 69, 1);
        margin-bottom: 24rpx;
      }
    }
  }


  .balance-box {
    width: 100%;
    padding: 24rpx;
    border-radius: 32rpx;
    background-color: #fff;
    .amount_text {
      width: 100%;
      text-align: left;
      margin-bottom: 16rpx;
      color: rgba(138, 43, 41, 1);
      font-size:30rpx;
    }
    .balance {
      width: 100%;
      height: 74rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .total {
        flex: 1;
        color: rgba(232, 54, 46, 1);
        font-size: 64rpx;
        font-weight: bold;
        display: inline-block;
        .ellip();
        .sm {
          font-size:34rpx;
          font-weight: normal;
          margin-left: 8rpx;
          display: inline-block;
        }
      }
      .withdraw-button {
        width: 164rpx;
        height: 74rpx;
        color: rgba(255, 255, 255, 1);
        background: linear-gradient(270deg, rgba(255, 76, 82, 1) 0%, rgba(254, 110, 76, 1) 100%);
        border-radius:60rpx;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .icon {
          width: 32rpx;
          height: 32rpx;
        }
        .with_pay_txt {
          font-size:30rpx;
          margin-left: 8rpx;
        }
      }
      .dis-btn {
        background: #FFC6C8 !important;
      }
    }
    .details {
      background-color: rgba(255, 245, 238, 1);
      border-radius: 24rpx;
      padding: 16rpx 24rpx;
      height: auto;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
      .tan_icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      .note {
        flex: 1;
        color: rgba(208, 75, 73, 1);
        font-size: 26rpx;
        line-height: 36rpx;
        text-align: left;
      }
    }
  }

  .balance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;
    margin-bottom: 20rpx;

    .amount {
      font-size: 28rpx;
      color: #666;
    }

    .total {
      font-size: 48rpx;
      color: #FF6000;
    }

    .withdraw-button {
      width: 120rpx;
      height: 60rpx;
      border-radius: 30rpx;
      background-color: #FF6000;
      color: #FFFFFF;
      font-size: 24rpx;
    }
  }

  .details {
    margin-top: 20rpx;
    text-align: center;
    color: #999;
  }

  .progress-box {
    width: 100%;
    height: 74rpx;
    padding: 16rpx 24rpx 16rpx 24rpx;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;

    .text {
      color: rgba(138, 43, 41, 1);
      font-size: 30rpx;
      margin-right: 32rpx;
    }
    
    .progress-bar {
      width: 300rpx;
      height: 8rpx;
      background-color: rgba(233, 237, 243, 1); /* 进度条背景颜色 */
      border-radius: 20rpx;
      overflow: hidden;
    }
    
    .progress-inner {
      height: 100%;
      border-radius: 20rpx;
      background-color: rgba(255, 78, 82, 1); /* 进度条填充颜色 */
      transition: width 0.5s ease-in-out; /* 平滑过渡效果 */
    }

    .num-percentage {
      font-size: 30rpx;
      font-weight: bold;
    }
  }


  .no-friends {
    color: rgba(138, 43, 41, 1);
    font-size: 30rpx;
    margin: 59rpx 237rpx;
  }
  

  .friend-progress {
    padding: 0 24rpx;
    display: flex;
    flex-direction: column;
    max-height: 1240rpx;

    .friends-item {
      display: flex;
      flex-direction: column;
      border-bottom: 1rpx solid rgba(233, 237, 243, 1);
      padding: 32rpx 0;

      .top {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 72rpx;

        .left-cover {
          flex: 1;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          .avatar {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            margin-right: 16rpx;
          }

          .name {
            font-size: 30rpx;
            color: rgba(138, 43, 41, 1);
            display: inline-block;
            margin-right: 8rpx;
            font-weight: bold;
          }
  
          .phone {
            font-size: 30rpx;
            color: rgba(138, 43, 41, 1);
            display: inline-block;
            font-weight: bold;
          }
        }
        .action-button {
          /* width: 144rpx !important;*/
          /* padding: 15rpx !important;*/
          /* border-radius: 48rpx;*/
          /* background-color: white;*/
          /* color: rgba(232, 54, 46, 1);*/
          /* border: 2rpx solid rgba(232, 54, 46, 1);*/
          /* font-size: 30rpx !important;*/
          /* display: flex;*/
          /* align-items: center;*/
          /* justify-content: center;*/
          width: 96rpx !important;
          padding: 15rpx 24rpx !important;
          height: 42rpx;
          box-sizing: content-box;
          border-radius: 48rpx;
          background-color: white;
          color: #e8362e;
          border: 2rpx solid #e8362e;
          font-size: 30rpx !important;
        }
        .done {
          color: rgba(232, 54, 46, 0.3);
          border: 2rpx solid rgba(232, 54, 46, 0.4);
        }
      }

      .center {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        height: 44rpx;
        margin-top: 20rpx;

        .tag {
          font-size: 26rpx;
          color: rgba(255, 137, 4, 1);
          font-weight: bold;
          width: 120rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 2rpx solid rgba(255, 239, 222, 1);
          border-radius: 8rpx;
          margin-right: 8rpx;
        }

        .des {
          font-size: 26rpx;
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .check-details {
        height: 32rpx;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        margin-top: 16rpx;
        .status_icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
        .check-note {
          color: rgba(0, 0, 0, 0.45);
          font-size: 26rpx;
        }
      }
      
    }
  }
}


/** 隐藏scroll-view滚动条 */
scroll-view ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}