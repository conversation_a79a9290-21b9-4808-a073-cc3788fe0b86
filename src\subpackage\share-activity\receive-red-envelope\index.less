page {
  background-color: rgba(255, 235, 224, 1);
}

.page-wrap {
  min-height: 100vh;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: top;
  background-size: 100vw 1200rpx;
  background-image: url('https://cdn.yupaowang.com/yupao_app/recieve_bgx.png');
}


.title {
  width: 100%;
  height: 204rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 59rpx 0;
  position: relative;
  .title_img{
    width: 524rpx;
    height: 100%;
  }
}

.red-container {
  width: 690rpx;
  height: 796rpx;
  margin-left: 30.8rpx;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 690rpx 796rpx;
  background-image: url('https://cdn.yupaowang.com/yupao_app/new_red_bg3x.png');
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .pack-title-img {
    width: 284rpx;
    height: 64rpx;
    position: absolute;
    left: 28.8%;
    top: 0;
  }
  
  .red-envelopes {
    width: fit-content;
    font-weight: bold;
    text-align: center;
    font-size: 32rpx;
    color: rgba(232, 54, 46, 1);
    margin-top: 130rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-end;
    margin-left: 8rpx;
    .amount_num {
      font-size: 144rpx;
      line-height: 144rpx;
    }
    .amount {
      font-size: 50rpx;
      line-height: 88rpx;
      margin-left: 8rpx;
    }
  }

  .float-tag {
    width: 130rpx;
    height: 52rpx;
    background-color: rgba(255, 227, 212, 1);
    border-radius: 16rpx 16rpx 16rpx 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 88rpx;
    top: 110rpx;
    z-index: 2;
    .envelope-icon {
      width: 32rpx;
      height: 32rpx;
    }
    .with_pay {
      color: rgba(190, 33, 0, 1);
      font-size: 26rpx;
      margin-left: 4rpx;
    }
  }

  .invitation-message {
    width: fit-content;
    height: 44rpx;
    font-weight: bold;
    text-align: center;
    font-size: 32rpx;
    color: rgba(138, 43, 41, 1);
    margin-top: 24rpx;
    .amount {
      font-size: 30rpx;
      color: rgba(232, 54, 46, 1);
    }
  }

  .tips-message {
    width: fit-content;
    min-height: 36rpx;
    text-align: center;
    font-size: 26rpx;
    color: rgba(0, 0, 0, 0.45);
    margin-top: 8rpx;
    .amount {
      font-size: 26rpx;
      color: rgba(232, 54, 46, 1);
    }
  }

  .check-status-text {
    width: 100%;
    height: 36rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8rpx;
    .s-icon {
      width: 32rpx;
      height: 32rpx;
    }
    .txt {
      color: rgba(0, 0, 0, 0.45);
      font-size: 26rpx;
      margin-left: 4rpx;
    }
  }

  .congratulation-status-text {
    width: 344rpx;
    height: 74rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 292rpx;
    left: 172rpx;
    background-color: rgba(0, 0, 0, 0.65);
    border-radius: 16rpx;
    z-index: 4;
    .c-icon {
      width: 32rpx;
      height: 32rpx;
    }
    .txt {
      color: rgba(255, 255, 255, 1);
      font-size: 30rpx;
      margin-left: 8rpx;
    }
    .triangle {
      width: 0;
      height: 0;
      border-left: 12rpx solid transparent;
      border-right: 12rpx solid transparent;
      border-top: 12rpx solid rgba(0, 0, 0, 0.65);
      position: absolute;
      bottom: -10rpx;
      left: 48%;
      z-index: 4;
    }
  }

  
  .invite-button {
    display: flex;
    flex-direction: column;
    width: 400rpx;
    position: absolute;
    left: 145rpx;
    bottom: 72rpx;

    .button {
      width: 400rpx;
      height: 84rpx;
      line-height: 56rpx;
      text-align: center;
      font-size: 34rpx;
      color: rgba(176, 12, 16, 1);
      background: linear-gradient(180deg, rgba(255, 236, 200, 1) 0%, rgba(255, 200, 140, 1) 100%);
      border-radius:64rpx;
    }
    .button_view {
      width: 396rpx;
      height: 80rpx;
      text-align: center;
      font-size: 34rpx;
      color: rgba(255, 234, 197, 1);
      background-color: transparent;
      border: 2rpx solid rgba(255, 234, 197, 1);
      border-radius:64rpx;
      margin-top: 24rpx;
      font-weight: 400 !important;
    }
    .disable-btn {
      background: rgba(255, 236, 200, 1) !important;
      color: rgba(226, 153, 132, 1) !important;
    }
  }
}