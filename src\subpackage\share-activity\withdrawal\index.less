page {
  background-color: rgba(255, 234, 229, 1);
  padding-bottom: 48rpx;
}

.page-wrap {
  min-height: 100vh;
  box-sizing: border-box;
  background: linear-gradient(180deg, rgba(255, 41, 92, 1) 0%, rgba(255, 68, 89, 1) 31.54%, rgba(255, 167, 149, 1) 58.62%, rgba(255, 234, 229, 1) 100%);
}



.card-wrap {
  width: 100%;
  padding: 0 24rpx;
  .withdraw-list {
    min-height: 58vh;
  }
  .card {
    width: 702rpx;
    height: auto;
    background-color: #fff;
    border-radius: 32rpx;
    margin-top: 16rpx;
    display: flex;
    flex-direction: column;

    .title-img {
      width: 100%;
      height: 46rpx;
      margin-top: 40rpx;
    }

    .no-friends {
      width: 100%;
      color: rgba(138, 43, 41, 1);
      font-size: 30rpx;
      text-align: center;
      margin-top: 59rpx;
    }
  }


  .balance-box {
    width: 100%;
    padding: 32rpx 24rpx 24rpx 24rpx;
    border-radius: 32rpx;
    background-color: #fff;
    .balance {
      width: 100%;
      height: 74rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .total {
        flex: 1;
        color: rgba(232, 54, 46, 1);
        font-size: 64rpx;
        font-weight: bold;
        display: inline-block;
        .ellip();
        .sm {
          font-size:34rpx;
          font-weight: normal;
          margin-left: 8rpx;
          display: inline-block;
        }
      }
      .withdraw-button {
        width: 164rpx;
        height: 74rpx;
        color: rgba(255, 255, 255, 1);
        background: linear-gradient(270deg, rgba(255, 76, 82, 1) 0%, rgba(254, 110, 76, 1) 100%);
        border-radius:60rpx;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .icon {
          width: 32rpx;
          height: 32rpx;
        }
        .with_pay_txt {
          font-size:30rpx;
          margin-left: 8rpx;
        }
      }
      .disabled-btn {
        background: #FFC6C8 !important;
      }
    }
    .could-pay {
      height: 42rpx;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      margin-top: 16rpx;
      margin-bottom: 32rpx;
      .note-left {
        color: rgba(138, 43, 41, 1);
        font-size: 30rpx;
        display: inline-block;
      }
      .number-note {
        color: rgba(232, 54, 46, 1);
        font-size: 30rpx;
        display: inline-block;
      }
    }
    .details {
      background-color: rgba(255, 245, 238, 1);
      border-radius: 24rpx;
      padding: 16rpx 24rpx;
      height: auto;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
      .tan_icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      .note {
        flex: 1;
        color: rgba(208, 75, 73, 1);
        font-size: 26rpx;
        line-height: 36rpx;
        text-align: left;
      }
    }
  }

  

  .list-box {
    padding: 0 24rpx;
    display: flex;
    flex-direction: column;
    max-height: 800rpx;
    overflow-y: auto;

    .item {
      display: flex;
      flex-direction: column;
      border-bottom: 1rpx solid rgba(233, 237, 243, 1);
      padding: 39rpx 0;
      box-sizing: border-box;
      height: 172rpx;

      .top {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-end;
        height: 48rpx;

        .title-left {
          font-size: 30rpx;
          color: rgba(0, 0, 0, 0.85);
          display: inline-block;
          margin-right: 8rpx;
          line-height: 42rpx;
        }
        .title-num{
          color: rgba(232, 54, 46, 1);
          font-size: 34rpx !important;
          font-weight: bold;
          margin-right: 40rpx;
        }
      }

      .bottom-text {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
        height: 44rpx;
        margin-top: 20rpx;

        .tag {
          font-size: 26rpx;
          color: rgba(6, 181, 120, 1);
          font-weight: bold;
          width: 152rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 2rpx solid rgba(185, 230, 215, 1);
          border-radius: 8rpx;
          height: 44rpx;
          .icon {
            width: 32rpx;
            height: 32rpx;
          }
        }

        .des {
          font-size: 26rpx;
          color: rgba(0, 0, 0, 0.45);
          line-height: 36rpx;
        }
      }
      
    }
  }
}