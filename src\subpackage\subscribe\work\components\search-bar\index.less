.tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.right {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.65);
}

.col {
  color: #0092ff;
  margin: 0 6rpx;
  font-weight: bold;
}

.input {
  margin-top: 32rpx;
  height: 80rpx;
  display: flex;
  border-radius: 16rpx;
  overflow: hidden;
}

.region {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f6fa;
  width: calc(100% - 136rpx);
  padding: 0 16rpx 0 20rpx;
  font-size: 28rpx;
}

.placeholder {
  /* min-width: calc(100% - 136rpx - 40rpx);*/
  width: 95%;
  color: rgba(0, 0, 0, 0.85);
}

.input-right {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.clear-icon {
  z-index: 1;
}

.total {
  margin-left: 24rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 28rpx;
}

.add {
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 146, 255, 1);
  font-size: 28rpx;
  width: 136rpx;
  color: rgba(255, 255, 255, 0.95);
  z-index: 1;
}

.plus {
  font-weight: bold;
}

.disabled {
  background: rgba(153, 211, 255, 1);
}

.tips-o {
  display: flex;
  flex-direction: column;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
}

.tip-img {
  width: 64rpx;
  height: 64rpx;
  border-radius: 64rpx;
  margin-right: 24rpx;
}

.tip-right {
  display: flex;
  flex-direction: column;
}

.tip-title {
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.tip-des {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.45);
}

.classify-o {
  display: flex;
  align-items: center;
  margin-top: 46rpx;
  font-size: 30rpx;
}

.cl-label {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  margin-right: 16rpx;
}

.cl-num {
  font-weight: bold;
  color: rgba(0, 146, 255, 1);
}

.cl-total {
  color: rgba(0, 0, 0, 0.65);
}