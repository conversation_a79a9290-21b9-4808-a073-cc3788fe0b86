<modal-body closeIcon="{{false}}" visible="{{visible}}" catch:exited="exitedAnime">
  <view class="picker">
    <view class="title">
      <view>将在你选择的时间段内为你推送新职位信息</view>
      <view>(最多可选2个时间段)</view>
    </view>
    <grid cols="{{2}}" xGap="12rpx" yGap="14rpx">
      <view
        wx:for="{{customTime}}"
        wx:key="id"
        class="item {{active[index] == item.id ? 'active' : ''}}"
        style="grid-column: span {{item.id === 1 ? 2 : 0}} / span {{item.id === 1 ? 2 : 0}};"
        data-item="{{item}}"
        data-index="{{index}}"
        bind:tap="onHandleClick"
      >
        <text>{{item.explain}} {{item.time}}</text>
      </view>
    </grid>
    <view class="footer">
      <button class="cancel" bind:tap="onClose">
        取消
      </button>
      <button class="sure" bind:tap="onConfirm">
        确定
      </button>
    </view>
  </view>
</modal-body>
