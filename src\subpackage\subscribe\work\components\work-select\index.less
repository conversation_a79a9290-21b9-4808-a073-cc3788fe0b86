.container {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
}

.slt-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.slt-left {
  display: flex;
  align-items: center;
}

.slt-right {
  display: flex;
  align-items: center;
}

.slt-laba {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
}

.slt-label-text {
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  overflow: hidden;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  max-width: 348rpx;
}

.no-t {
  color: rgba(0, 0, 0, 0.45);
}

.r-bb {
  display: flex;
}

.slt-jt {
  margin-right: 24rpx;
}

.slt-shux {
  width: 1rpx;
  height: 32rpx;
  background: rgba(204, 204, 204, 1);
  margin-right: 24rpx;
}

.count {
  background: #f74742;
  box-sizing: content-box;
  display: flex;
  align-items: center;
  justify-content: center;
  /* line-height: 40rpx;*/
  border-radius: 500rpx;
  text-align: center;
  color: #fff;
  padding: 6rpx;
  font-weight: bold;
  font-size: 26rpx;
  height: 30rpx;
  min-width: 30rpx;
  margin-right: 44rpx;
}
