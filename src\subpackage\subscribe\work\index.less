.container {
  padding: 24rpx;
  background-color: #fff;
  margin: 0 24rpx;
  margin-top: 16rpx;
  border-radius: 16rpx;
}

.blank {
  height: 100rpx;
  width: 100%;
}

.field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 24rpx;
  margin: 0 24rpx;
  margin-top: 16rpx;
  border-radius: 16rpx;
}

.left {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.65);
}

.right {
  display: flex;
  justify-content: center;
  align-items: center;
}

.l-notice {
  display: flex;
  flex-direction: column;
}

.l-expect {
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.area {
  margin-right: -12rpx;
  flex: 1;
  overflow: hidden;
  justify-content: flex-end !important;
}

.city {
  flex: 1;
  text-align: right;
  margin-left: 24rpx;
  /* white-space: nowrap;*/
  /* overflow: hidden;*/
  /* text-overflow: ellipsis;*/
}

.text {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
}

.no-text {
  color: rgba(0, 0, 0, 0.45);
}

.icon {
  position: absolute;
  right: -20rpx;
  top: -20rpx;
  width: 48rpx;
  height: 48rpx;
}

.title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.title-lbimg {
  margin-right: 16rpx;
}

.des {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 16rpx;
}

.title-search {
  width: 208rpx;
  height: 64rpx;
  border-radius: 50rpx;
  background: rgba(255, 255, 255, 0.6);
  border: 1rpx solid rgba(151, 151, 151, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.85);
  /* margin-right: 10rpx;*/
}

.title-search-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.empty-btn {
  padding: 0 24rpx;
  height: 80rpx;
  background-color: #0092ff;
  border-radius: 8rpx;
  margin: 30rpx auto 0;

  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  letter-spacing: 5rpx;
  text-align: center;
  line-height: 80rpx;
  transition: 0.2s;

  &:active {
    opacity: 0.8;
  }
}

.subscribe-vip-box {
  background-color: #fff;
  padding: 24rpx;
  margin: 0 24rpx;
  margin-top: 16rpx;
  border-radius: 16rpx;
}

.vip-img {
  width: 654rpx;
  height: 296rpx;
}

.vip-btn {
  width: 520rpx;
  height: 88rpx;
  display: flex;
  place-content: center;
  place-items: center;
  border-radius: 200rpx;
  background: linear-gradient(180deg, #27a3ff 0%, #0092ff 100%);
  box-shadow: 0rpx 4rpx 16rpx #1c9eff4d;
  display: flex;
  justify-items: center;
  align-items: center;
  color: #fff;
  font-weight: bold;
  font-size: 34rpx;
  margin: 20rpx auto 8rpx;
}

.vip-icon {
  margin-left: 8rpx;
}

.focus {
  min-height: calc(100vh - 120rpx);
}