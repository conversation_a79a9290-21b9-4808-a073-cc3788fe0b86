page {
  background-color: #f5f6fa;
  position: relative;
  .safe-area(12rpx);
}
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
}
.img-box {
  width: 241rpx;
  height: 286rpx;
  position: absolute;
  top: 100rpx;
  right: 0;
  z-index: 99;
}

.head-box {
  background-color: #ecf2fe;
  padding: 32rpx;
  .head-desc {
    padding-top: 16rpx!important;
  }
}

.body {
  padding: 0 24rpx;
  background: linear-gradient(180deg, #ecf2fe 0%,#f5f5f7 100%);
  background-size: 100% 186rpx;
  background-repeat: no-repeat;
}
.card {
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  background: #ffffff;
  margin-bottom: 24rpx;
}

.card-tit {
  line-height: 42rpx;
  font-weight: bold;
  font-size: 30rpx;
  padding-bottom: 8rpx;
}
.card-desc {
  line-height: 42rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  padding-bottom: 16rpx;
}
.card-img-box {
  padding-bottom: 16rpx ;
  .card-img {
    height: 180rpx;
    border-radius: 12rpx;
  }
}
.card-dd {
  line-height: 40rpx;
  color: rgba(0, 0, 0, 0.65);
  font-size: 26rpx;
  padding-bottom: 8rpx;
  display: flex;
  &::before {
    content: '';
    display: block;
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background: #99d3ff;
    margin-top: 16rpx;
    margin-right: 16rpx;
    white-space: nowrap;
  }
}
