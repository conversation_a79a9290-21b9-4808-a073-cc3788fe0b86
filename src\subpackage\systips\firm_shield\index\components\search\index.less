

.search-box {
  width: 100%;
  left: 0;
  top: 100rpx;
  z-index: 10;
  padding: 0 32rpx;
  background-color: #fff;
  display: flex;
  position: fixed;
  flex-direction: column;
  margin-top: 12rpx;
}



.input-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .input-left {
    flex: 1;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(245, 247, 252, 1);
    border-radius: 16rpx;
    padding-left: 24rpx;

    .input {
      font-size: 30rpx;
      width: 100%;
      color: rgba(0, 0, 0, 0.85);
      display: block;
      height: 72rpx;
      flex: 1;
      margin-left: 16rpx;
    }

    .input-clear {
      opacity: 0.7;
      padding: 6rpx 24rpx;
      display: inline-flex;
    }
  }
  .cancel-text {
    height: 72rpx;
    line-height: 72rpx;
    width: 60rpx;
    font-size: 30rpx;
    color: rgba(0, 0, 0, 0.65);
    margin-left: 32rpx;
  }
}


.search-manuel {
  width: 100%;
  padding: 0 0rpx 140rpx 0rpx;
  background-color: #fff;
  height: 75vh;
  padding-top: 24rpx;

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    width: 100%;
    .logo-img {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 24rpx;
    }
    .no-data-text {
      color: rgba(0, 0, 0, 0.45);
      font-size: 26rpx;
    }
  }

  .search-item {
    height: 112rpx;
    padding: 34rpx 0;
    font-size: 30rpx;
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1rpx solid rgba(233, 237, 243, 1);

    .search-item-title {
      .textrow(1);
      overflow: hidden;
      word-break: break-all;
    }
  }
  .have-blocked-item {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    min-height: 112rpx;
    padding: 28rpx 0;
    border-bottom: 1rpx solid rgba(233, 237, 243, 1);
    .left {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .left-text {
        max-width: 470rpx;
        font-size: 30rpx;
        color: rgba(0, 0, 0, 0.45);
        .ellip()
      }
      .left-tag {
        font-size: 20rpx;
        color: rgba(0, 0, 0, 0.45);
        font-weight: bold;
        border: 1rpx solid rgba(233, 237, 243, 1);
        background-color: rgba(245, 246, 250, 1);
        border-radius: 8rpx;
        padding: 4rpx 8rpx;
        margin-left: 8rpx;
      }
    }
    .right {
      width: fit-content;
      font-size: 26rpx;
      color: rgba(0, 146, 255, 1);
      font-weight: bold;
      background-color: #fff;
      border-radius: 40rpx;
      padding: 10rpx 24rpx;
      border: 2rpx solid rgba(0, 146, 255, 1);
      margin-left: 24rpx;
    }
  }
}
.hidden {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}

::-webkit-scrollbar {
  display: none;
}
