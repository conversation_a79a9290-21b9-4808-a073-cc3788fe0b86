page {
  background-color: #fff;
}

.info {
  width: 100%;
  min-height: 416rpx;
  background: rgba(247, 71, 66, 0.1);
  padding: 64rpx 32rpx 32rpx;
  text-align: center;
  box-sizing: border-box;
}

.box {
  padding: 32rpx;
  border-radius: 16rpx;
  background: #fff;
  color: rgba(0, 0, 0, 0.85);
  font-size: 32rpx;
  line-height: 48rpx;
  width: 100%;
  margin-top: 64rpx;
}

.red {
  color: #F74742;
}

.tips {
  padding: 48rpx 32rpx 0;
}

.tip {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
}

.button-box {
  margin-top: 16rpx;
  text-align: center;
}

.blue-tip {
  font-size: 32rpx;
  color: #0092FF;
}