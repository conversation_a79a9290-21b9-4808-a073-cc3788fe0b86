page {
  background-color: #fff;
}
.safe-area {
  .safe-area(32rpx);
}
.list-title {
  padding: 32rpx 16rpx;
  font-size: 32rpx;
  background-color: #e8f3ff;
  font-weight: bold;
}

.list-desc {
  font-size: 28rpx;
  font-weight: bold;
}

.list-item {
  padding: 18rpx 48rpx;
  display: flex;
  align-items: center;
  font-weight: bold;
  .bottom-line();
  &.warning {
    color: @warn-color;
  }
}

.title {
  flex: 1;
  font-size: 28rpx;
  font-weight: bold;
}

.btn-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding: 24rpx;
  grid-gap: 24rpx;

  .btn {
    .btn();
    display: flex;
    align-items: center;
    justify-content: center;
    height: auto !important;
    box-sizing: border-box !important;
    min-height: 82rpx;
    line-height: 46rpx;
    font-size: 28rpx;
    background-color: @primary-color;
    color: white;
    border-radius: 8rpx !important;
    font-weight: bold;
    padding: 12rpx !important;

    .active();
  }
}

.box-test {
  padding: 32rpx;
  .break-all();
}

.clear-box {
  padding: 32rpx;
  display: flex;
  align-items: center;
}

.clear-input {
  flex: 1;
  margin-right: 24rpx;
  border: 1px solid #f5f5f5;
  border-radius: 16rpx;
  height: 72rpx;
  padding: 0 24rpx;
}
