<view class="list-title">
  <view>当前版本：<text style="color: #0092ff">v {{version}}</text></view>
  <block wx:for="{{listEnv}}" wx:key="value">
    <view class="list-desc" style="color: #0092ff" wx:if="{{item.value === thatEnv}}">
      当前所选择的环境：{{item.name}}
    </view>
  </block>
  <block wx:for="{{listEnv}}" wx:key="value">
    <view class="list-desc" style="color: #969799" wx:if="{{item.value === buildEnv}}">
      打包时所选择的环境：{{item.name}}
    </view>
  </block>
  <view style="color:#f74742;font-size: 28rpx">
    当前设备平台标识：{{systemInfo.platform}}
  </view>

  <view style="color:#f74742;font-size: 28rpx">
    环境切换之后请务必：点击右上角的菜单按钮 --> 选择（重新进入小程序）之后再进行测试
  </view>
</view>
<block wx:for="{{listEnv}}" wx:key="value">
  <view class="list-item" bind:tap="onSelectEnv" data-item="{{item}}">
    <view class="title">{{item.name}}</view>
    <m-switch  active="{{thatEnv === item.value}}" />
  </view>
</block>
<view class="list-item warning" bind:tap="onSelectLogin">
  <view class="title">开启静默登录: 生产环境此设置无效</view>
  <m-switch active="{{DEV_STATIC_LOGIN}}" activeColor="#f74742" />
</view>
<view class="clear-box">
  <input class="clear-input" value="{{delStoreKey}}" bind:input="onInputDel" placeholder="输入缓存key，清除指定的缓存" />
  <m-button custom-class="btn" bind:tap="onDeleteStorage">删除缓存</m-button>
</view>
<view class="btn-box">
  <m-button custom-class="btn" bind:tap="onClearStorage">清空缓存</m-button>
  <m-button custom-class="btn" bind:tap="onLogStorage">打印缓存</m-button>
  <m-button custom-class="btn" bind:tap="onDelDialog">清空弹框</m-button>
  <m-button custom-class="btn" bind:tap="onMessage">消息订阅</m-button>
  <m-button custom-class="btn" bind:tap="onClearGuidingTipsNumStorage">引导次数</m-button>
  <m-button custom-class="btn" bind:tap="onClearIndexGetDialogStorage">首页时限</m-button>
  <m-button custom-class="btn" bind:tap="onCopyInfo">系统信息</m-button>
  <m-button custom-class="btn" bind:tap="openClickArea">{{ pageClickBorder ? '关闭' : '开启' }}点击范围</m-button>
  <m-button custom-class="btn" bind:tap="onABOpen">{{ abOpenState ? '关闭' : '打开' }}AB按钮</m-button>
</view>
<view class="box-test">
  {{systemInfoStr}}
</view>

<view class="safe-area"></view>
