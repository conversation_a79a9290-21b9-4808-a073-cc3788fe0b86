.greeting-item {
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  width: 100vw;
}

.greeting-v {
  display: flex;
  align-items: center;
  border-bottom: 0.5px solid rgba(233, 237, 243, 1);
  width: 100%;
  height: 100%;
  padding: 34rpx 0;
}

.greeting-item-sort {
  height: 124rpx;
  padding: 0 32rpx;
}

.greeting-left {
  width: 100%;
  box-sizing: border-box;
}
.greeting-right {
  display: flex;
  align-items: center;
}

.greeting-sort {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.cws-del-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
  background: rgba(232, 54, 46, 1);
  margin-left: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.greeting-content {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.85);
  width: 100%;
  overflow-wrap: break-word;
}
.greeting-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.gc-s {
  .textrow(1);
}

.edit-btn {
  color: rgba(0, 0, 0, 0.65);
  font-size: 26rpx;
  display: flex;
}

.edit-icon {
  margin-right: 12rpx;
}

.move-df-icon {
  padding: 8rpx;
  border-radius: 8rpx;
  background: rgba(224, 243, 255, 1);
  margin-left: 48rpx;
}

.moving-icon {
  margin-left: 48rpx;
}

.greeting-shadow {
  box-shadow: 0px 0px 20px rgba(46, 53, 59, 0.25);
}
