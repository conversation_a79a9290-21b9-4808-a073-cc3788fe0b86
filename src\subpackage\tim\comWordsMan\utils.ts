import { actions, dispatch, store } from '@/store/index'

export function addGreeting() {
  wx.$.nav.push(
    '/subpackage/tim/comWordsOper/index',
    {},
    async (data) => {
      const { content } = data || {}
      const { greetingList } = store.getState().comwords
      await dispatch(actions.comwordsActions.setState({ greetingList: [content, ...(greetingList || [])] }))
      this.onInit()
    },
  )
}

export function editGreeting(greeting) {
  const { content: oContent, id } = greeting || {} as any
  wx.$.nav.push(
    '/subpackage/tim/comWordsOper/index',
    {},
    async (data) => {
      const { content } = data || {}
      const { greetingList } = store.getState().comwords
      const nGreetingList = [...(greetingList || [])]
      const idx = nGreetingList.findIndex(gt => gt.id == id)
      const item = nGreetingList[idx]
      nGreetingList[idx] = { ...item, content }
      await dispatch(actions.comwordsActions.setState({ greetingList: [...(nGreetingList || [])] }))
      this.onInit()
    },
    {
      content: oContent,
      title: '编辑常用语',
      id,
      type: 2,
    },
  )
}

export function delGreeting(greeting) {
  const { id } = greeting || {} as any
  wx.showLoading({ title: '处理中...' })
  wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/delete']({ id }).then(async (res) => {
    wx.hideLoading()
    const { code, error, message } = res
    if (code == 0) {
      const { greetingList } = store.getState().comwords
      const nGreetingList = [...(greetingList || [])]
      const idx = nGreetingList.findIndex(gt => gt.id == id)
      nGreetingList.splice(idx, 1)
      await dispatch(actions.comwordsActions.setState({ greetingList: [...(nGreetingList || [])] }))
      this.onInit()
      return
    }
    if (error && message) {
      wx.$.msg(message)
    }
  }).catch((err) => {
    wx.hideLoading()
    const { error, message } = err || {}
    let msg = '删除失败,请稍后重试'
    if (error && message) {
      msg = message
    }
    wx.$.msg(msg)
  })
}

export function saveSortGreeting() {
  const { greetingList } = store.getState().comwords
  const { sortLoading, dataList } = this.data
  if (sortLoading) return
  const sortDataList = dataList.map((item, idx) => ({ id: item.id, sort: idx + 1 }))
  const oSortDataList = greetingList.map((item, idx) => ({ id: item.id, sort: idx + 1 }))
  if (JSON.stringify(sortDataList) == JSON.stringify(oSortDataList)) {
    this.setData({ sortLoading: false, isSort: false, currItem: null })
    return
  }
  this.setData({ sortLoading: true })
  wx.showLoading({ title: '处理中' })
  wx.$.javafetch['POST/reach/v2/im/userGreeting/sortGreeting']({ sortDataList }).then(async () => {
    wx.hideLoading()
    this.setData({ sortLoading: false, isSort: false, currItem: null })
    dispatch(actions.comwordsActions.setState({ greetingList: [...(dataList || [])] }))
  }).catch(() => {
    wx.hideLoading()
    this.setData({ sortLoading: false })
    wx.$.msg('保存失败,请稍后重试')
  })
}

export function getFooterHeight() {
  wx.createSelectorQuery()
    .select('#footer')
    .boundingClientRect((rect) => {
      // 使页面滚动到底部
      this.setData({ footerHeight: rect?.height || 0 })
    })
    .exec()
}

export function getScrollViewHeight() {
  wx.createSelectorQuery()
    .select('#scrollview')
    .boundingClientRect((rect) => {
      const { height } = rect || {}
      const sData:any = { _scrollViewHeight: height || 0, _endScroll: height || 0, _startScroll: 0 }
      // 使页面滚动到底部
      this.setData(sData)
    })
    .exec()
}
