page {
  background-color: #fff;
}

.body {
  padding: 24rpx 32rpx 0;
}

.textarea {
  font-size: 30rpx;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  display: block;
  word-break: break-word;
  white-space: pre-wrap;
  line-height: 50rpx;
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx;
  background-color: #fff;
  .safe-area(24rpx);
  border-top: 1rpx solid rgba(233, 237, 243, 1);
  &.default-pb {
    padding-bottom: 24rpx;
  }
}

.info-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;

  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: @primary-color;
  }
  .num-err {
    color: @error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.btn {
  width: 96rpx;
  height: 56rpx;
  border-radius: 8rpx;
  border: 2rpx solid rgba(0, 146, 255, 1);
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
  font-size: 26rpx;
  margin-left: 20rpx;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
}

.dbt {
  border: 2rpx solid rgba(0, 0, 0, 0.25);
  color: rgba(0, 0, 0, 0.25);
}
