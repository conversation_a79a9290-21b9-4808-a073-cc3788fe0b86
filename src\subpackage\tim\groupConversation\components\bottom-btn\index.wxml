<!-- 快捷功能 -->
<view wx:if="{{isShortcutShow}}" class="shortcut-fun" catch:touchmove="onDisableMove">
  <view class="shortcut-btn" wx:for="{{shortcutbtnData}}" wx:for-item="sbtn" wx:key="index" wx:if="{{sbtn.type=='call' ? isShowTelBtn : true}}" data-type="{{sbtn.type}}" bind:tap="onShorcutBtn">
    <icon-font custom-class="shortcut-icon" type="{{sbtn.icon}}" size="25rpx" color="rgba(0, 146, 255, 1)" />
    <text class="shortcut-btn-txt">{{sbtn.name}}</text>
  </view>
</view>
<!-- 聊天功能 -->
<view class="chat-fun">
  <icon-font custom-class="icon-btn" type="{{isRecorderShow?'yp-icon_srk_yuyin':'yp-icon_srk_jp'}}" size="56rpx" bind:tap="onChatTypeClick" />
  <view class="chat-input-v {{isRecorderShow ?'':'no-display'}}">
    <textarea 
      id="chat-input-textarea-va" 
      class="chat-input" 
      confirm-type="send" 
      show-confirm-bar="{{false}}" 
      confirm-hold="{{true}}" 
      adjust-position="{{false}}" 
      auto-height="{{true}}"  
      focus="{{inputFocus}}" 
      maxlength="{{-1}}" 
      value="{{chat}}" 
      disable-default-padding 
      bindkeyboardheightchange="bindkeyboardheightchange" 
      bindinput="onInputChange" 
      bindblur="onTtBlur" 
      bindconfirm="onChatSend"  
    />
    <view class="changyy-zyw"></view>
    <icon-font bind:tap="onShorcutBtn" data-type="kjhf" custom-class="changyy-icon" type="yp-changyy" size="48rpx" color="rgba(0, 0, 0, 0.85)" />
  </view>
  <view class="chat-input-v {{yyBtnPress?'yuyin-btn-click':''}} {{isRecorderShow ?'no-display':''}}">
    <view class="chat-yuyin-btn" bind:touchstart="onTouchstart" bind:touchend="onTouchend" bind:longpress="onLongpress" bind:touchmove="onTouchmove">
      {{yyBtntext}}
    </view>
  </view>
  <icon-font custom-class="face-icon" type="{{emShow?'yp-icon_srk_jp':'yp-icon_srk_bqb'}}" size="56rpx" bind:tap="onEmojiShow" />
  <icon-font custom-class="icon-btn" wx:if="{{!chat}}" type="{{toolbarShow?'yp-icon_srk_jp':'yp-icon_srk_more'}}" size="56rpx"  bind:tap="onMoreToolBar" />
  <icon-font custom-class="icon-btn" wx:else type="yp-imsend2" color="rgba(0, 146, 255, 1)" size="56rpx" bind:tap="onChatSend" />
</view>
<view style="height:{{keyboardHeight}}px;width:2rpx"></view>
<!-- 表情包 -->
<emoji-swiper wx:if="{{emShow}}" chat="{{chat}}" bind:emojiClick="onEmojiClick" bind:emojidel="onEmojiDel" bind:send="onChatSend"/>
<yuyin-gif wx:if="{{yyBtnPress}}" moveNe="{{yuYinGif}}" btmTxt="{{btmTxt}}" />
