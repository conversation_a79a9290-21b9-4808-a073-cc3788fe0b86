.common-words-nodata {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 492rpx;
  overflow: hidden;
}
.cws-nodata-img {
  width: 334rpx;
  height: 130rpx;
}

.cws-nodata-txt {
  font-size: 26rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.65);
  margin-top: 32rpx;
}

.cws-nodata-btn {
  width: 480rpx;
  height: 80rpx;
  background: rgba(0, 146, 255, 1);
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 32rpx;
}

.common-words-data {
  width: 100vw;
}

.greeting-list {
  height: 380rpx;
}

.cws-greeting-footer {
  display: flex;
  align-items: center;
  height: 112rpx;
  width: 100%;
}

.cwsg-btn {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cwsh-text{
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 28rpx;
}

.cwsg-s {
  width: 2rpx;
  height: 32rpx;
  background: rgba(245, 246, 250, 1);
}

.cwsg-icon{
  margin-right: 8rpx;
}