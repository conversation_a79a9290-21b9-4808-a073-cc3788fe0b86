<drawer wx:if="{{visible}}" visible isMaskClose isDisableMove catch:close="onClose" catch:hide="onClose">
  <view class="wrap">
    <view id="header-top">
      <view class="header">
        <view class="title">选择原因，为您优化推荐</view>
        <icon-font type="yp-guanbi" size="48rpx" color="rgba(0, 0, 0, 0.25)" catch:tap="onClose" />
      </view>
      <view class="auto-reply-v">
        <view class="auto-reply">
          <view class="ar-top">
            <view class="art-left">{{ enable ? '礼貌通知对方' : '未开启不合适自动回复'}}</view>
            <view class="art-right" catch:tap="onOpen">
              <text class="art-btn">{{enable ? '修改' : '去开启'}}</text>
              <icon-font type="yp-msg_set_mianbaoxue" size="32rpx" color="rgba(0, 146, 255, 1)" />
            </view>
          </view>
          <view class="ar-desc">{{ reply ? reply : ""}}</view>
        </view>
      </view>
    </view>
    <scroll-view  scroll-y="{{true}}" style="max-height:calc(100vh - {{headHeight + ((topHeight + footerHeight + 20) || 161)}}px)">
      <view class="sv-body">
        <view wx:for="{{dislikeList}}" wx:key="key" class="sv-item" data-item="{{item}}" bind:tap="onDislikeClick">{{item.reason}}</view>
      </view>
    </scroll-view>
    <view id="footer-ms">
      <m-stripes />
    </view>
  </view>
</drawer>