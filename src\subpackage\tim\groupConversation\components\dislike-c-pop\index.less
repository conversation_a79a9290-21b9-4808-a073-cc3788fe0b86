.wrap {
  position: fixed;
  bottom: 0;
  background: rgba(255, 255, 255, 1);
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  width: 100vw;
  z-index: 20;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
}

.sv-body {
  padding: 0 32rpx;
}

.sv-item {
  display: flex;
  align-items: center;
  padding: 35rpx 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}
