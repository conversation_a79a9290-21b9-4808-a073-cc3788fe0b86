.emoji {
  position: relative;
  background: rgba(245, 246, 250, 1);
}

.emoji-scroll {
  height: 568rpx;
}

.emoji-v {
  padding: 24rpx;
}

.emoji-cotent {
  margin: -15rpx -16rpx;
  display: flex;
  flex-wrap: wrap;
}

.fixed-em {
  width: 164rpx;
  height: 280rpx;
  position: fixed;
  z-index: 100;
  background-image: url("https://cdn.yupaowang.com/yupao_common/ff4e9d96.png");
  background-size: contain;
  background-repeat: no-repeat;
}

.fixed-em-content {
  padding: 40rpx 40rpx 0;
}

.fixed-emoji-img {
  width: 88rpx;
  height: 88rpx;
}

.fixed-emoji-txt {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
}

.emoji-img {
  width: 72rpx;
  height: 72rpx;
  margin: 15rpx 16rpx;
}

.emoji-btn-v {
  position: absolute;
  right: 0;
  bottom: 0;
}

.emoji-btn-top {
  width: 322rpx;
  height: 48rpx;
  background: linear-gradient(
    180deg,
    rgba(245, 246, 250, 0) 0%,
    rgba(245, 246, 250, 1) 100%
  );
}

.emoji-btn-btm {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 26rpx;
  width: 322rpx;
  height: 120rpx;
  background: rgba(245, 246, 250, 1);
}

.emoji-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 128rpx;
  height: 72rpx;
  border-radius: 16rpx;
}

.emoji-btn-del {
  background: rgba(255, 255, 255, 1);
}

.emoji-btn-send {
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 32rpx;
}

.emoji-btn-txt-no {
  color: rgba(0, 0, 0, 0.25);
  background: rgba(255, 255, 255, 1);
}

.del-img {
  width: 48rpx;
  height: 48rpx;
}
