/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */

Component({
  properties: {
    chat: { type: String, value: '' },
  },
  data: {
    EmojiList: [],
    // 默认显示区域高度 即:删除按钮到scroll-view顶部的高度
    cHeight: 160,
    emojiHeight: 36,
    bgIndex: -1,
    bgName: '',
    bgFixed: { left: 0, top: 0 },
  },
  lifetimes: {
    async ready() {
      const EmojiList = await wx.$.l.NewEmojiData()
      this.setData({ EmojiList })
    },
  },
  methods: {
    onEmojiClick(e) {
      const { em } = e.currentTarget.dataset
      this.setData({ bgIndex: -1 })
      if (em) {
        this.triggerEvent('emojiClick', { em: `${em}` })
      }
    },
    onEmojiDel() {
      this.triggerEvent('emojidel')
    },
    onSend() {
      this.triggerEvent('send')
    },
    onEmojiLongPress(e) {
      const { index, em } = e.currentTarget.dataset
      wx.createSelectorQuery().in(this)
        .selectAll(`#emojiImg${index}`)
        .boundingClientRect((rects: any) => {
          if (wx.$.u.isArrayVal(rects)) {
            const rect = rects[0]
            const { left, top } = rect || {}
            this.setData({ bgIndex: index, bgName: (em || '').replace('[', '').replace(']', ''), bgFixed: { left, top } })
          }
        })
        .exec()
    },
  },
})
