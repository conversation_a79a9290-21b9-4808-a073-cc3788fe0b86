<!-- 表情 -->
<view class="emoji">
  <scroll-view 
    enhanced
    scroll-y 
    show-scrollbar="{{false}}"
    class="emoji-scroll"
  >
    <view class="emoji-v">
        <view class="emoji-cotent">
          <view wx:for="{{EmojiList}}" wx:key="index" catch:longpress="onEmojiLongPress" catch:tap="onEmojiClick" data-em="{{item}}" data-index="{{index}}">
            <image id="emojiImg{{index}}" lazy-load="{{true}}" data-index="{{index}}" class="emoji-img" src="../emoji-new/{{index}}.png" />
          </view>
        </view>
    </view>
  </scroll-view>
  <view class="emoji-btn-v">
      <view class="emoji-btn-top"></view>
      <view class="emoji-btn-btm">
        <view class="emoji-btn emoji-btn-del" catch:tap="onEmojiDel">
            <image wx:if="{{chat.length > 0}}" lazy-load="{{true}}" class="del-img" src="../emoji/1001.png" />
            <image wx:else lazy-load="{{true}}" class="del-img" src="../emoji/1002.png" />
        </view>
        <view class="emoji-btn emoji-btn-send {{chat.length == 0 ? 'emoji-btn-txt-no' : ''}}" catch:tap="onSend">发送</view>
      </view>
  </view>
  <view  class="fixed-em" wx:if="{{bgIndex >= 0}}" style="top: {{bgFixed.top - 92}}px;left: {{bgFixed.left - 24}}px;">
    <view class="fixed-em-content" catch:tap="onEmojiClick" data-em="[{{bgName}}]" data-index="{{bgIndex}}">
      <image lazy-load="{{true}}" data-index="{{index}}" class="fixed-emoji-img" src="../emoji-new/{{bgIndex}}.png" />
      <view class="fixed-emoji-txt">{{bgName}}</view>
    </view>
  </view>
</view>