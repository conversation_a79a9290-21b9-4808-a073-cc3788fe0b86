.wrap {
  position: fixed;
  bottom: 0;
  background: rgba(255, 255, 255, 1);
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  width: 100vw;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
}

.body {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 32rpx;
}

.item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.img {
  width: 96rpx;
  height: 96rpx;
}

.txt {
  margin-top: 16rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 26rpx;
}

.txt-no{
  color: rgba(0, 0, 0, 0.25);
}