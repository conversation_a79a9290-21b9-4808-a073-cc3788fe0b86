/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 换电话
 */

import { store } from '@/store/index'
import { applyExchange } from '../../utils'

function maskWechatID(wechatID) {
  if (!wechatID) {
    return ''
  }
  if (wechatID.length <= 5) {
    return wechatID
  }
  const start = wechatID.slice(0, 3) // 取前3位
  const end = wechatID.slice(-2) // 取末2位
  const middle = '*'.repeat(wechatID.length - 5) // 用星号代替中间部分，星号数量为总长度减去5
  return `${start}${middle}${end}`
}

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    conversation: { type: Object, value: {} },
  }

  observers = {
    visible(v) {
      if (v) {
        const { userInfo } = store.getState().user
        const { userBaseObj } = userInfo || {} as any
        const { wechatNumber = '' } = userBaseObj || {}
        this.setData({ wechatNumber: maskWechatID(wechatNumber) })
      }
    },
  }

  data = {
    // 用户手机号
    wechatNumber: '',
  }

  onEditInfo() {
    this.onClose()
    wx.$.r.push({ path: '/subpackage/member/info/index' })
  }

  onClose() {
    this.triggerEvent('close')
  }

  async onConfirm() {
    await wx.$.u.waitAsync(this, this.onConfirm, [], 1000)
    const { wechatNumber } = this.data
    if (!wechatNumber) {
      this.onClose()
      this.triggerEvent('updatewechat')
      return
    }
    const { conversation } = store.getState().timmsg
    const { conversationId } = conversation || {}
    if (!conversationId) {
      wx.$.msg('会话异常,请稍后重试').then(() => {
        wx.$.r.back()
      })
      return
    }

    applyExchange('EXCHANGE_WECHAT', {}, {
      success: () => {
        this.onClose()
      },
    })
  }
})
