.msg-txt-v {
  position: relative;
  display: flex;
  align-items: center;
}
.msg-txt-v-l {
  height: 100%;
}
.msg-txt-v-r {
  height: 100%;
}

.msg-txt {
  position: relative;
  max-width: 474rpx;
  padding: 24rpx;
  background: #0092ff;
  color: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  font-size: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 48rpx;
}

.msg-other {
  background: #ffffff;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 24rpx 24rpx 24rpx 4rpx;
}

.arrow {
  position: absolute;
  top: 19px;
  width: 0;
  height: 0;
  font-size: 0;
  border: solid 8rpx;
}

.arrow-r {
  right: -8px;
  border-color: transparent transparent transparent #0092ff;
}

.arrow-l {
  left: -8px;
  border-color: transparent #ffffff transparent transparent;
}

.audio-v {
  display: flex;
  align-items: center;
}

.audio-other {
  justify-content: flex-start;
}

.audio-self {
  justify-content: flex-end;
}

.image-play-yuyin {
  width: 48rpx;
  height: 48rpx;
}

.img-right {
  margin-left: 16rpx;
}

.triangle {
  transform: rotate(180deg);
}

.seco-txt {
  font-size: 34rpx;
}
