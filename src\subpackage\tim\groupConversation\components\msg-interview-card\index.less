.msg-main {
  display: flex;
  justify-content: flex-end;
  margin: 0rpx 24rpx 40rpx;
}

.main-start {
  justify-content: flex-start;
}

.card-one {
  display: flex;
  align-items: center;
  border-radius: 24rpx 24rpx 4rpx 24rpx;
  background: rgba(0, 146, 255, 1);
  padding: 26rpx 24rpx;
}

.card-one-txt {
  color: rgba(255, 255, 255, 1);
  font-size: 32rpx;
  line-height: 44rpx;
}
.card-one-img {
  width: 48rpx;
  height: 48rpx;
  margin-left: 16rpx;
}

.card-two {
  display: flex;
  align-items: center;
  border-radius: 24rpx 24rpx 24rpx 4rpx;
  background: rgba(255, 255, 255, 1);
  padding: 26rpx 24rpx;
}

.card-two-txt {
  color: rgba(0, 0, 0, 0.85);
  font-size: 32rpx;
  line-height: 44rpx;
}

.card-two-img {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.card-v {
  position: relative;
  padding: 32rpx 24rpx;
  width: 542rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 1);
  border: 1rpx solid rgba(153, 211, 255, 1);
}

.card-top-bg {
  border-radius: 24rpx;
  position: absolute;
  top: 0;
  left: 0;
  width: 542rpx;
  height: 160rpx;
  background: linear-gradient(
    180deg,
    rgba(0, 146, 255, 0.13) 0%,
    rgba(224, 243, 255, 0) 100%
  );
}

.card-head {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.card-icon-v {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.card-img {
  width: 32rpx;
  height: 32rpx;
}

.card-txt {
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
  font-size: 30rpx;
  line-height: 42rpx;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 32rpx;
}

.btn {
  width: 240rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 28rpx;
}

.refuse {
  background: rgba(245, 247, 252, 1);
}

.agree {
  background: rgba(224, 243, 255, 1);
}

.refuse-cl {
  background: rgba(245, 246, 250, 1);
  color: rgba(0, 0, 0, 0.25);
}

.agree-cl {
  background: rgba(224, 243, 255, 1);
  border: 2rpx solid rgba(0, 146, 255, 1);
}

.card-item {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

.card-item-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.card-item-label {
  flex: 1;
  color: rgba(0, 0, 0, 0.85);
  font-size: 26rpx;
  line-height: 36rpx;
  .ellip();
}
