/*
 * @Date: 2022-02-09 11:02:56
 * @Description: 普通招工详情，底部tabbar
 */

import { actions, dispatch } from '@/store/index'

let timer
Component({
  properties: {
    /** 招工详情，接口内容，及自定义状态 */
    msgInfo: {
      type: Object,
      value: {},
    },
    isShowTk: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    tkWith: 0,
    isSigleEm: false,
    isOk: false,
  },
  observers: {
    msgInfo(v) {
      const { payload } = v || {}
      const { renderDom } = payload || {}
      if (wx.$.u.isArrayVal(renderDom) && renderDom.length == 1) {
        const { isNew, name } = renderDom[0]
        if (name == 'img' && isNew) {
          this.setData({ isSigleEm: true, isOk: true })
          return
        }
      }
      this.setData({ isOk: true })
    },
  },
  methods: {
    onTkClick(e) {
      this.setData({ isShowTk: false })
      const { type } = e.detail
      if (type == 'copy') {
        this.copy()
      }
    },
    onLongpressMsg(e) {
      const { id } = e.currentTarget
      dispatch(actions.timmsgActions.setState({ longpressid: id }))
      if (timer) {
        clearTimeout(timer)
      }
      const query = this.createSelectorQuery()
      // 选择id
      query.select(`#${id}`).boundingClientRect()
      query.exec((res) => {
        this.setData({ isShowTk: true, tkWith: res[0].width })
        timer = setTimeout(() => {
          this.setData({ isShowTk: false })
        }, 3000)
      })
    },
    copy() {
      let txt = ''
      const { msgInfo } = this.data
      console.log('msgInfo:', msgInfo)
      if (!!msgInfo.payload.renderDom && msgInfo.payload.renderDom.length > 0) {
        msgInfo.payload.renderDom.forEach((item) => {
          txt += item.text
        })
      } else {
        txt = msgInfo.payload.text
      }
      wx.setClipboardData({
        data: txt,
        success() {},
      })
    },
    onStatusClcik(e) {
      const { msgInfo } = this.data
      this.triggerEvent('statusclcik', { msgInfo, type: e.detail.type })
    },
  },
})
