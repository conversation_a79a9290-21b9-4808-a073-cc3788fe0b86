.btn-v {
  background: rgba(255, 255, 255, 1);
  width: 100%;
  height: 112rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.btn-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.img {
  width: 40rpx;
  height: 40rpx;
}

.img-bg {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  background-image: url("https://cdn.yupaowang.com/yupao_common/6df85d36.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-top: 8rpx;
}

.txt {
  color: rgba(0, 0, 0, 0.65);
  font-size: 26rpx;
  margin-top: 4rpx;
}

.txt-no {
  color: rgba(0, 0, 0, 0.45);
}

.uiv-txt {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
  font-size: 22rpx;
  margin-top: 8rpx;
}

.uiv-time-txt {
  position: absolute;
  top: -4rpx;
  left: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2rpx;
  height: 20rpx;
  border-radius: 5rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 16rpx;
}
