import { handleAction } from '../../midUtils'
import { RootState, store } from '@/store/index'
import { getMyAttach } from './utils'
import { handleExchange } from '../../utils'
import dayjs from '@/lib/dayjs/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { dealDialogApi } from '@/utils/helper/dialog/index'

Component(class extends wx.$.Component {
  useStore(state: RootState) {
    const { storage } = state
    return {
      role: storage.userChooseRole,
    }
  }

  properties = {
    conversation: {
      type: Object,
      value: {},
    },
    query: {
      type: Object,
      value: {},
    },
  }

  data = {
    infoDetail: {},
    cardInfo: {},
    rightsStatusInfo: {} as any,
    otherStatusInfo: {} as any,
    inviteStatus: {} as any,
    // 是否显示附件简历
    isExChangeResumeFileShow: false,
    // 是否拉黑
    toUserBlock: false,
    // 简历附件数据
    fileList: [],
    // 消息卡片的ID
    exchangeMsgId: '',
    // 即将面试日期和时间
    upcgIntvw: {
      day: '',
      time: '',
    },
  }

  observers = {
    conversation(v) {
      if (wx.$.u.isEmptyObject(v)) return
      const { infoDetail = {}, rightsStatusInfo = {}, toUserBlock, otherStatusInfo = {} } = v || {}
      const { cardInfo } = infoDetail || {}
      const { inviteStatus } = otherStatusInfo || {}
      let { startTime } = inviteStatus || {}
      const upcgIntvw: any = { day: '31', time: '20:55' }
      if (startTime) {
        if (`${startTime}`.length == 10) {
          startTime *= 1000
        }
        const date = dayjs(startTime)
        upcgIntvw.day = date.format('DD')
        upcgIntvw.time = date.format('HH:mm')
      }
      this.setData({ rightsStatusInfo, infoDetail, cardInfo, toUserBlock, otherStatusInfo, inviteStatus, upcgIntvw })
    },
  }

  onChange() {
    this.triggerEvent('change')
  }

  onRefreshConversation() {
    this.triggerEvent('refresh')
  }

  onChangeTelOrWechatClick() {
    const { conversation } = store.getState().timmsg
    const { rightsStatusInfo } = conversation || {}
    const { exchangeTel, exchangeWechat } = rightsStatusInfo || {}
    const { status, forbiddenMsg } = exchangeTel || {}
    const { status: wxStatus } = exchangeWechat || {}

    if (status == 0 && wxStatus == 0 && forbiddenMsg) {
      wx.$.msg(forbiddenMsg)
      return
    }
    this.triggerEvent('changetelorwechat')
  }

  async onExChangeResumeFileClick() {
    await wx.$.u.waitAsync(this, this.onExChangeResumeFileClick, [], 2000)
    this.onExChangeResumeFile()
  }

  onExChangeResumeFileClose() {
    this.setData({ isExChangeResumeFileShow: false, fileList: [], exchangeMsgId: '' })
  }

  async onBossInterviewClick(e) {
    await wx.$.u.waitAsync(this, this.onBossInterviewClick, [e], 2000)
    const { name } = e.currentTarget.dataset
    if (name) {
      wx.$.collectEvent.event('Interview_button_click', {
        button_name: name,
      })
    }
    this.onBossInterviewHandle()
  }

  async onBossInterviewHandle() {
    const { inviteStatus, conversation } = this.data as DataTypes<typeof this>
    const { conversationId, toUserId, infoDetail } = conversation || {} as any
    const { status, isReply, hasImRight, existJob, inviteId, dialogIdentify } = inviteStatus || {}
    const { infoId, infoType } = infoDetail || {}
    // 显示约面试按钮的状态
    if (!status || status == '-999') {
      if (!isReply) {
        wx.$.msg('双方回复之后才可使用')
        return
      }
      if (!hasImRight) {
        handleAction.call(this, conversation, {
          type: 'msg',
          chatsend: () => {
            this.onBossInterviewHandle()
          },
        })
        return
      }
      if (!existJob) {
        const pageCode = getPageCode()
        const popup = await dealDialogApi({ dialogIdentify: dialogIdentify || 'msyywkxzw', pageCode, template: {}, isRule: true })
        if (popup) {
          wx.$.showModal({
            ...popup,
            pageCode,
            success: (popRes) => {
              const { routePath } = popRes
              if (routePath == 'release') {
                wx.$.r.push({ path: '/subpackage/recruit/fast_issue/index/index' })
              }
            },
          })
          return
        }
      }
      wx.$.r.push({
        path: '/subpackage/invite-interview/interview-info-edit-b/index',
        query: { conversationId, toUserId, infoId, infoType },
      })
    } else {
      wx.$.r.push({
        path: '/subpackage/invite-interview/interview-info-b/index',
        query: { inviteId },
      })
    }
  }

  async onWorkerInterviewClick(e) {
    await wx.$.u.waitAsync(this, this.onWorkerInterviewClick, [e], 2000)
    const { name } = e.currentTarget.dataset
    if (name) {
      wx.$.collectEvent.event('Interview_button_click', {
        button_name: name,
      })
    }
    this.onWorkerInterviewHandle()
  }

  onWorkerInterviewHandle() {
    const { inviteStatus } = this.data as DataTypes<typeof this>
    const { inviteId } = inviteStatus || {}
    wx.$.r.push({
      path: '/subpackage/invite-interview/interview-info-c/index',
      query: { inviteId },
    })
  }

  async onExChangeResumeFile(isCall = false) {
    const { conversation } = store.getState().timmsg
    const { rightsStatusInfo } = conversation || {}
    const { exchangeResumeFile } = rightsStatusInfo || {}
    const { value, status, forbiddenMsg, hasImRight } = exchangeResumeFile || {}
    if (forbiddenMsg) {
      wx.$.msg(forbiddenMsg)
      return
    }
    if (!isCall && hasImRight == 0 && !value) {
      handleAction.call(this, conversation, {
        type: 'msg',
        chatsend: () => {
          this.onExChangeResumeFile(true)
        },
      })
      return
    }
    if (status == 1 || status == 3) {
      const { userChooseRole } = store.getState().storage
      const sData: any = { isExChangeResumeFileShow: true }
      if (userChooseRole == 2) {
        const res = await getMyAttach()
        const { fileList } = res
        if (wx.$.u.isArrayVal(fileList)) {
          sData.fileList = fileList
        }
      }
      this.setData(sData)
    }
  }

  // 牛人点击消息卡片同意发送附件
  async onWorkerSendResumeFile(exchangeMsgId) {
    const sData: any = { isExChangeResumeFileShow: true, exchangeMsgId }
    const res = await getMyAttach()
    const { fileList } = res
    if (wx.$.u.isArrayVal(fileList)) {
      const len = fileList.length
      if (len == 1) {
        const file = fileList[0]
        const { uuid } = file || {}
        handleExchange({ exchangeMsgId, exchangeType: 'EXCHANGE_RESUME_FILE', agree: true }, { resumeFileId: uuid })
      } else {
        sData.fileList = fileList
        this.setData(sData)
      }
    }
  }

  contactBtnText() {
    this.triggerEvent('contactBtnText')
  }

  onDisLikeClick() {
    const { conversation, role } = this.data as DataTypes<typeof this>
    const { infoDetail, toUserId, conversationId } = conversation || {} as any
    const { relatedInfoId } = infoDetail || {}
    if (role == 1) {
      wx.$.collectEvent.event('boss_reject_candidate_click', {
        sub_resume_id: `${relatedInfoId}`,
        job_seeker_user_id: `${toUserId}`,
        conversation_id: `${conversationId}`,
      })
    } else {
      wx.$.collectEvent.event('job_seeker_disinterest_click', { job_id: `${relatedInfoId}`, conversation_id: `${conversationId}` })
    }
    this.triggerEvent('dislike')
  }

  /** 禁止滚动操作 */
  onDisableMove() { }
})
