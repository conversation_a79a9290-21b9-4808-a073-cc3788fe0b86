.title-class {
  margin-right: 8rpx !important;
  max-width: 190rpx !important;
}

.body {
  display: flex;
  flex-direction: column;
  position: relative;
}

.sv-v-w-height {
  height: 40rpx;
  width: 1rpx;
}

.c-txt {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  justify-content: center;
  margin: 0 0 40rpx;
}

.btmbtn-body {
  position: fixed;
  bottom: 0;
  min-height: 112rpx;
  background: #fff;
  width: 100vw;
}

.btmbtn-body-o {
  opacity: 0;
}

.scroll_view_btm {
  height: 1rpx;
}

.msg-main {
  display: flex;
  justify-content: flex-end;
  margin: 0rpx 24rpx 40rpx;
}

.main-start {
  justify-content: flex-start;
}

.msg-sys {
  justify-content: center;
}

.recharge-tip {
  height: 84rpx;
  background: #f5f6fa;
  width: 100vw;
  font-size: 30rpx;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  padding: 0 32rpx;
  display: flex;
  align-items: center;
}

.recharge-tip-text {
  & + .recharge-tip-text {
    margin-left: 4rpx;
  }
}

.btnBg {
  background: #f5f6fa;
}

.ch-label-v {
  display: flex;
  align-items: center;
}

.ch-label {
  padding: 4rpx 8rpx;
  height: 44rpx;
  flex-shrink: 0;
  border-radius: 8rpx;
  background: rgba(255, 239, 222, 1);
  color: rgba(255, 137, 4, 1);
  font-size: 26rpx;
  line-height: 36rpx;
  margin-right: 5rpx;
}
