<custom-header title="{{conversation.toUserRemark || conversation.toUserName || '加载中...'}}" title-class="title-class" bind:titleClick="onAvaterClick">
  <view class="ch-label-v" slot="after" >
    <view wx:if="{{conversation.otherStatusInfo.notMatchStatus.exist}}" class="ch-label" >{{role == 1 ? '不合适' : '不感兴趣'}}</view>
    <icon-font type="yp-sj_right" size="16rpx" color="rgba(0, 0, 0, 0.85)" />
  </view>
</custom-header>
<skeleton wx:if="{{isShowSkeleton}}" />
<view class="body" id="group_conversation_msg_bdv">
  <top-btn
    id="topbtn"
    conversation="{{conversation}}"
    query="{{query}}"
    bind:change="onTopBtnChange"
    bind:refresh="onRefreshConversation"
    bind:contactBtnText="contactBtnText"
    bind:changetelorwechat="onShowChangeTelOrWechatPop"
    bind:dislike="onDislikePop"
  />
  <scroll-view bind:tap="onScrollViewClick" scroll-into-view="{{scrollLocId}}" refresher-enabled="{{true}}" bindrefresherrefresh="onPullDownRefresh" refresher-triggered="{{refresher}}" style="height:calc(100vh - {{headHeight + chatBtmHeight + chatTopHeight}}px)" scroll-y="{{true}}">
    <view class="sv-v-w-height"></view>
    <view wx:if="{{isHasOldMsg || !moreMes}}" class="c-txt">以下是30天内的聊天记录</view>
    <view wx:if="{{isShowTime}}" class="c-txt">{{startTime}}</view>
    <block wx:for="{{messageList}}" wx:for-item="msgInfo" wx:key="id">
      <view wx:if="{{msgInfo.type == 'TIMCustomElem' && ( msgInfo.payload.data.type == '1' || msgInfo.payload.data.type == '2')}}" id="{{msgInfo.id}}">
          <recruit-card wx:if="{{msgInfo.payload.data.type == '1' && isShowCard}}" msgInfo="{{msgInfo}}"/>
          <resumes-card wx:if="{{ msgInfo.payload.data.type == '2' && isShowCard }}" msgInfo="{{msgInfo}}"/>
      </view>
      <view wx:elif="{{msgInfo.type == 'TIMCustomElem' &&msgInfo.payload.data.type == '3'}}" id="{{msgInfo.id}}">
        <msg-resume-card msgInfo="{{msgInfo}}" catch:click="onResCardMsgClick"/>
      </view>
      <view wx:elif="{{msgInfo.payload.data.type == '610.1' && msgInfo.pType == 'TimChatMsg'}}" id="{{msgInfo.id}}" data-item="{{msgInfo}}" class="{{msgInfo.payload.data.content.subType == 3 ? 'msg-exchange' : ''}}">
          <msg-exchange-tel msgInfo="{{msgInfo}}" />
      </view>
      <view wx:elif="{{msgInfo.payload.data.type == '660.1' && msgInfo.pType == 'TimChatMsg'}}" id="{{msgInfo.id}}" data-item="{{msgInfo}}" class="{{msgInfo.payload.data.content.subType == 3 ? 'msg-exchange' : ''}}">
          <msg-exchange-wechat msgInfo="{{msgInfo}}" catch:addwechat="onAddWechat"/>
      </view>
      <view wx:elif="{{msgInfo.payload.data.type == '710.1' && msgInfo.pType == 'TimChatMsg'}}" id="{{msgInfo.id}}" data-item="{{msgInfo}}" class="{{(msgInfo.payload.data.content.fileInfo.fileName || msgInfo.payload.data.content.title) && (msgInfo.payload.data.content.subType == 3 || (msgInfo.isSelf && msgInfo.payload.data.content.subType == 1 && role == 2))? 'msg-exchange' : ''}}">
         <msg-exchange-resumefile msgInfo="{{msgInfo}}" bind:bossreqfile="onBossReqFile" />
      </view>
      <view wx:elif="{{msgInfo.payload.data.type == '910.1' && msgInfo.pType == 'TimChatMsg'}}" id="{{msgInfo.id}}">
        <msg-interview-card  msgInfo="{{msgInfo}}"/>
      </view>
      <view wx:else id="{{msgInfo.id}}">
        <view class="msg-main {{msgInfo.isSelf?'':'main-start'}}" wx:if="{{msgInfo.pType == 'TimChatMsg'}}">
          <msg-head-img wx:if="{{!msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
          <msg-text isShowTk="{{isShowTk}}" msgInfo="{{msgInfo}}" wx:if="{{msgInfo.type === 'TIMTextElem' }}" bind:statusclcik="onStatusClcik" />
          <msg-map msgInfo="{{msgInfo}}" wx:if="{{msgInfo.type === 'TIMMapElem' }}" bind:statusclcik="onStatusClcik" />
          <msg-custom isShowTk="{{isShowTk}}" msgInfo="{{msgInfo}}" wx:elif="{{msgInfo.type === 'TIMCustomElem'}}" catch:onMsgBtnClick="onMsgBtnClick" />
          <msg-audio isShowTk="{{isShowTk}}" bind:statusclcik="onStatusClcik" bind:onAudioId="onAudioId" catch:audioPause="audioPause" catch:audioPlay="audioPlay" ispPlay="{{isPlay}}" msgInfo="{{msgInfo}}" wx:elif="{{msgInfo.type === 'TIMSoundElem'}}" data-id="{{msgInfo.id}}" data-oId="{{msgInfo.oId}}" data-isRead="{{msgInfo.isRead}}" data-isSelf="{{msgInfo.isSelf}}"  audioId="{{audioId}}" />
          <msg-image msgInfo="{{msgInfo}}" bind:statusclcik="onStatusClcik" bind:bindload="bindload" wx:elif="{{msgInfo.type === 'TIMImageElem'}}" />
          <msg-head-img wx:if="{{msgInfo.isSelf && msgInfo.isAvatar}}" msgInfo="{{msgInfo}}" />
        </view>
        <view class="msg-main msg-sys" wx:if="{{msgInfo.pType == 'SystemMsg'}}">
          <msg-system msgInfo="{{msgInfo}}" catch:sysclick="onSysMsgClick" bind:reedit="onReEdit"/>
        </view>
      </view>
    </block>
    <view class="scroll_view_btm" id="group_msg_scroll_view_btm"></view>
  </scroll-view>
  <!-- 底部按钮 -->
  <view class="btmbtn-body {{!isShowSkeleton?'':'btmbtn-body-o'}}" id="group_conv_btm_btn_bodv">
    <follow-chat wx:if="{{conversation.isShowSayHelloAgain && role == 1}}" conversation="{{conversation}}" bind:edit="onShowFollowChatEdit" bind:close="onTopBtnChange"/>
    <bottom-btn
      id="btm-btn"
      isShowWhilte="{{!evaluateContentControl.show && !isUpdateWechatShow && !followChatEditShow}}"
      emojiShow="{{emojiShow}}"
      isShowTelBtn="{{showTelBtn && !conversation.toUserBlock}}"
      isShortcutShow="{{!conversation.otherStatusInfo.notMatchStatus.exist}}"
      inputFocus="{{inputFocus}}"
      toolbarShow="{{toolbarShow}}"
      bind:bindkeyboardheightchange="bindkeyboardheightchange"
      bind:keyboardblur="getChatBottomHeightOnly"
      bind:moreToolBarClick="onMoreToolBarChange"
      bind:shorcutBtn="onShorcutBtn"
      bind:chatTypeClick="onChatTypeClick"
      bind:emojiShow="onEmojiShow"
      bind:chatSend="onChatSend"
    />
    <!-- 常用语 -->
    <common-words wx:if="{{cwShow && keyboardHeight < 30}}" greetingList="{{greetingList}}" bind:delGreeting="onDelGreeting" bind:comWordsClick="onComWordsClick" />
     <!-- 底部工具栏 -->
    <toolbar wx:if="{{toolbarShow}}" query="{{query}}" conversation="{{conversation}}" toolbarList="{{toolbarIcon}}" pageCode="{{pageCode}}" bind:onToolChange="onToolChange" bind:contactBtnText="contactBtnText"/>
    <!-- 底部小横条 -->
    <view class="{{toolbarShow || emojiShow ? 'btnBg' : ''}}">
      <m-stripes  wx:if="{{keyboardHeight == 0}}"/>
    </view>
  </view>
</view>
<!-- 充值弹窗 -->
<recharge-popup show="{{showRechargePopup}}" bind:success="onRechargeSuccess" bind:close="onToggleRechargePopup">
  <view slot="header" class="recharge-tip" wx:if="{{rechargeContent.length > 0}}">
    <text wx:for="{{rechargeContent}}" wx:key="index" style="color: {{item.color}};" class="recharge-tip-text">{{item.text}}</text>
  </view>
</recharge-popup>
<civilization-pop popupWindowIdentify="{{popupWindowIdentify}}" bind:show="onCiviShow"/>
<update-wechat wx:if="{{isUpdateWechatShow}}" visible="{{isUpdateWechatShow}}" exchangeWxMsgId="{{exchangeWxMsgId}}" bind:close="onUpdateWechatClose" type="{{2}}" />
<!-- 更换职位弹框 -->
<select-position source="4" list="{{jobList}}" value="{{spJobId}}" visible="{{spVisible}}" isDesc="{{false}}" catch:confirm="onSpConfirm" catch:close="onSpClose" />
<!-- 牛人风险提示弹窗 / IM 聊天风险提示弹窗  共用一个弹窗组件 -->
<pop-recruit-risk  visible="{{isShowRiskAndImChatRisk}}" currDialog="{{riskContent}}" bind:close="onCloseRecruitRisk"/>
<!-- 修改追聊消息 -->
<follow-chat-edit wx:if="{{followChatEditShow}}" placeholder="请输入追聊消息" conversation="{{conversation}}" isFocus="{{false}}" value="{{followChatEditContent}}" maxContent="{{maxMessageWordCountLimit}}" visible="{{followChatEditShow}}" bind:close="onCloseFollowChatEdit"/>
<!-- 换电话换手机弹框 -->
<exchange-telorwechat-pop visible="{{exchangeTelOrWechatPopShow}}" query="{{query}}" conversation="{{conversation}}" bind:close="onCloseExchangeTelOrWechatPop" bind:contactBtnText="contactBtnText"/>
<!-- B端不合适弹框 -->
 <dislike-b-pop wx:if="{{dislikeBPopShow}}" visible="{{dislikeBPopShow}}" dislikeList="{{dislikeList}}" conversation="{{conversation}}" bind:dislikeadd="onRefreshGroupInfo" bind:close="onCloseDislikeBPop"/>
<!-- C端不感兴趣弹框 -->
 <dislike-c-pop wx:if="{{dislikeCPopShow}}" visible="{{dislikeCPopShow}}" dislikeList="{{dislikeList}}" conversation="{{conversation}}" bind:dislikeadd="onRefreshGroupInfo" bind:close="onCloseDislikeCPop"/>