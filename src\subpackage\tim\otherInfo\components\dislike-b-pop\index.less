.wrap {
  position: fixed;
  bottom: 0;
  background: rgba(255, 255, 255, 1);
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  z-index: 20;
  width: 100vw;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
}

.auto-reply-v {
  padding: 0 32rpx;
}

.auto-reply {
  padding: 24rpx;
  border-radius: 24rpx;
  background: rgba(245, 246, 250, 1);
}

.ar-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.art-left {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 30rpx;
}

.art-right {
  display: flex;
  align-items: center;
}

.art-btn {
  color: rgba(0, 146, 255, 1);
  font-size: 30rpx;
}

.ar-desc {
  margin-top: 16rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  .ellip();
}

.sv-body {
  padding: 0 32rpx;
}

.sv-item {
  display: flex;
  align-items: center;
  padding: 35rpx 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}
