/*
 * @Date: 2022-07-11 11:33:20
 * @Description: 追聊消息修改弹框
 */

const { top: topHeight, height } = wx.$.u.sInfo().menuRect

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    conversation: { type: Object, value: {} },
    // 不合适反馈来源页面 1 IM会话详情页 2 用户个人设置页
    sourcePage: { type: String, value: '1' },
    // 不合适理由列表
    dislikeList: { type: Array, value: [] },
  }

  observers = {
    visible(v) {
      if (v) {
        this.initReplyData()
        this.getHeight()
      }
    },
  }

  data = {
    headHeight: topHeight + height + 4,
    topHeight: 0,
    footerHeight: 0,
    // 自动回复语
    reply: '',
    // 是否开启自动招呼
    enable: false,
    // 系统招呼语id
    systemId: 0,
    // 自定义招呼语id
    customId: 0,
    // 自定义招呼语
    customList: [],
    // 系统招呼语
    systemList: [],
  }

  pageLifetimes = {
    show() {
      this.initReplyData()
    },
  }

  initReplyData() {
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/query']({ type: 5 }).then((res) => {
      const { data } = res || {}
      const { enable, systemId, customId, customList, systemList } = data || {}
      const sData:any = { enable, systemId, customId, customList, systemList }
      if (enable && customId) {
        const { content } = customList.find(item => item.id == customId) || {}
        sData.reply = content
      } else if (enable && systemId) {
        const { content } = systemList.find(item => item.id == systemId) || {}
        sData.reply = content
      }
      if (!enable && !sData.reply && wx.$.u.isArrayVal(systemList)) {
        const { content } = systemList[0]
        sData.reply = content
      }
      this.setData(sData)
    })
  }

  getHeight() {
    wx.createSelectorQuery()
      .in(this)
      .select('#header-top').boundingClientRect((rect) => {
        const { height } = rect || {}
        this.setData({ topHeight: height || 0 })
      })
      .exec()
    wx.createSelectorQuery()
      .in(this)
      .select('#footer-ms').boundingClientRect((rect) => {
        const { height } = rect || {}
        this.setData({ footerHeight: height || 0 })
      })
      .exec()
  }

  async onOpen() {
    await wx.$.u.waitAsync(this, this.onOpen, [], 1000)
    const { enable, systemId, customId, customList, systemList } = this.data
    if (enable) {
      wx.$.collectEvent.event('invalid_dialog_modify_click', { button_name: '修改自定义回复语' })
    }
    wx.$.r.push({ path: '/subpackage/tools-page/set-not-reply-lang/index', params: { enable, systemId, customId, customList, systemList } })
  }

  onDislikeClick(e) {
    const { item } = e.currentTarget.dataset
    const { conversation, sourcePage } = this.data as DataTypes<typeof this>
    const { infoDetail, toUserId, conversationId } = conversation || {} as any
    const { infoId, infoType, relatedInfoId, relatedInfoType } = infoDetail || {}
    const { key, reason } = item || {}
    const param = {
      infoId,
      infoType,
      relatedInfoId,
      relatedInfoType,
      toUserId,
      key,
      reason,
      sourcePage,
    }

    wx.$.collectEvent.event('boss_confirm_rejection', {
      sub_resume_id: `${relatedInfoId}`,
      job_seeker_user_id: `${toUserId}`,
      conversation_id: `${conversationId}`,
      rejection_reason: `${reason}`,
    })
    wx.$.l.updateOtherStatusInfo(conversation)
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/clues/v1/inappropriate/add'](param).then((res) => {
      wx.hideLoading()
      const { code, message } = res || {}
      if (code != 0) {
        wx.$.msg(message || '请求失败,请稍后重试')
        return
      }
      this.triggerEvent('dislikeadd')
    }).catch((err) => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '请求异常,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }

  /** 抽屉组件已收起 */
  onClose() {
    this.triggerEvent('close')
  }
})
