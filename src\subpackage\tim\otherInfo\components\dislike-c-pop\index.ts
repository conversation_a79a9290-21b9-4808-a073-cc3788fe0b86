/*
 * @Date: 2022-07-11 11:33:20
 * @Description: c端不感兴趣弹框
 */

const { top: cTopHeight, height: cheight } = wx.$.u.sInfo().menuRect

Component(class extends wx.$.Component {
  properties = {
    visible: { type: Boolean, value: false },
    conversation: { type: Object, value: {} },
    // 不感兴趣反馈来源页面 1 IM会话详情页 2 用户个人设置页
    sourcePage: { type: String, value: '1' },
    // 不感兴趣理由列表
    dislikeList: { type: Array, value: [] },
  }

  observers = {
    visible(v) {
      if (v) {
        this.getHeight()
      }
    },
  }

  data = {
    headHeight: cTopHeight + cheight + 4,
    topHeight: 0,
    footerHeight: 0,
  }

  getHeight() {
    wx.createSelectorQuery()
      .in(this)
      .select('#header-top').boundingClientRect((rect) => {
        const { height } = rect || {}
        this.setData({ topHeight: height || 0 })
      })
      .exec()
    wx.createSelectorQuery()
      .in(this)
      .select('#footer-ms').boundingClientRect((rect) => {
        const { height } = rect || {}
        this.setData({ footerHeight: height || 0 })
      })
      .exec()
  }

  /** 抽屉组件已收起 */
  onClose() {
    this.triggerEvent('close')
  }

  onDislikeClick(e) {
    const { item } = e.currentTarget.dataset
    const { conversation, sourcePage } = this.data as DataTypes<typeof this>
    const { infoDetail, toUserId, conversationId } = conversation || {} as any
    const { infoId, infoType, relatedInfoId, relatedInfoType } = infoDetail || {}
    const { key, reason } = item || {}
    const param = {
      infoId,
      infoType,
      relatedInfoId,
      relatedInfoType,
      toUserId,
      key,
      reason,
      sourcePage,
    }
    wx.$.collectEvent.event('job_seeker_disinterest_confirm', {
      job_id: `${relatedInfoId}`,
      conversation_id: `${conversationId}`,
      disinterest_reason: `${reason}`,
    })
    wx.$.l.updateOtherStatusInfo(conversation)
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/clues/v1/inappropriate/add'](param).then((res) => {
      wx.hideLoading()
      const { code, message } = res || {}
      if (code != 0) {
        wx.$.msg(message || '请求失败,请稍后重试')
        return
      }
      this.triggerEvent('dislikeadd')
    }).catch((err) => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '请求异常,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }
})
