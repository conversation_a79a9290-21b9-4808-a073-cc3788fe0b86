<custom-header title="{{(headTitle ? headTitle + '的个人资料' : '') || '加载中...'}}" />
<view class="body" wx:if="{{headTitle}}">
  <view class="other-info-v">
    <view class="other-info">
      <view class="head-img">
        <image mode="aspectFill" lazy-load="{{true}}" class="i-img" src="{{conversation.toUserAvatar}}" />
      </view>
      <view class="i-info">
        <view class="i-name">{{headTitle}}</view>
        <view class="i-txt">{{conversation.toUserName}} | {{conversation.toUserId	}}</view>
      </view>
    </view>
    <view class="dislike-tips" wx:if="{{conversation.otherStatusInfo.notMatchStatus.exist}}">
      <icon-font custom-class="dislike-icon" type="yp-icon_tusu" size="28rpx" color="rgba(255, 137, 4, 1)" />
      <view class="dislike-txt">{{conversation.otherStatusInfo.notMatchStatus.noticeMsg}}</view>
    </view>
  </view>
  <view class="set-item" bind:tap="toSetRemark">
    <view class="item-r-name">设置备注</view>
    <view class="item-r">
      <view class="item-r-txt {{ !conversation.toUserRemark? 'txt-h' : ''}}">{{conversation.toUserRemark || "去设置"}}</view>
      <icon-font type="yp-mbxl" size="40rpx" color="rgba(0, 0, 0, 0.25)" />
    </view>
  </view>
  <view class="set-item" bind:tap="onSignShow">
    <view class="item-r-name">标记聊天</view>
    <view class="item-r">
      <view class="item-r-txt">{{conversation.toUserDesc}}</view>
      <icon-font type="yp-mbxl" size="40rpx" color="rgba(0, 0, 0, 0.25)" />
    </view>
  </view>
  <view class="set-item"  wx:if="{{!conversation.otherStatusInfo.notMatchStatus.exist}}"> 
    <view class="item-r-name">置顶聊天</view>
    <view class="item-r">
      <m-switch active="{{isPinned}}" bind:click="onOprPin" />
    </view>
  </view>
  <view class="set-item item-p" wx:if="{{!conversation.otherStatusInfo.notMatchStatus.exist}}">
    <view class="item-r-name">消息免打扰</view>
    <view class="item-r">
      <m-switch active="{{isNotice}}" bind:click="setRemindType" />
    </view>
  </view>
  <view class="set-item item-n">
    <view class="item-t">
      <view class="item-r-name">将对方加入黑名单</view>
      <view class="item-r">
        <m-switch active="{{toUserBlock}}" bind:click="setUserBlock" />
      </view>
    </view>
    <view class="item-b">加入黑名单后，你将不再接收对方发送的信息并且无法再与对方创建联系</view>
  </view>
  <view class="set-item">
    <view class="item-r-name">标记为{{role == 1 ? '不合适' : '不感兴趣'}}</view>
    <view class="item-r">
      <m-switch active="{{conversation.otherStatusInfo.notMatchStatus.exist}}" bind:click="setDislike" />
    </view>
  </view>
  <view class="set-item item-p" bind:tap="toReport">
    <view class="item-r-name">举报他</view>
    <view class="item-r">
      <icon-font type="yp-mbxl" size="40rpx" color="rgba(0, 0, 0, 0.25)" />
    </view>
  </view>
</view>

<drawer visible="{{isWrap}}" isMaskClose catch:close="onWrapClose">
  <view class="wrap">
      <view class="wrap-top">
           <view class="wrap-title">将对方加入黑名单</view>
          <image class="wrap-close" catch:tap="onWrapClose" src="https://staticscdn.zgzpsjz.com/miniprogram/images/wyl/yupao_mini_pop_but_close_113x.png"/>
      </view>
      <view class="wrap-content">
          将对方加入黑名单后，你将不再接收对方发送的信息并且无法再与对方创建联系。可前往 <text class="wrap-txt">我的-隐私保护-黑名单-拉黑用户</text> 管理拉黑名单
      </view>
      <view class="wrap-check" catch:tap="onCheck">
          <icon-font wx:if="{{isCheck}}" type="yp-yescheck" size="40rpx" color="rgba(0, 146, 255, 1)"/>
          <icon-font wx:else type="yp-nocheck" size="40rpx" color="rgba(233, 237, 243, 1)"/>
          <text class="wrap-del-txt">同时删除和此人的聊天记录</text>
      </view>
      <view class="wrap-btn" catch:tap="saveUserBlock">确定</view>
      <m-stripes />
  </view>
</drawer>
<!-- 标记 -->
<sign visible="{{isSign}}" conversation="{{conversation}}" bind:close="onSignClose"/>
<!-- B端不合适弹框 -->
 <dislike-b-pop wx:if="{{dislikeBPopShow}}" visible="{{dislikeBPopShow}}" dislikeList="{{dislikeList}}" conversation="{{conversation}}" sourcePage="2" bind:dislikeadd="onDislikeAdd" bind:close="onCloseDislikeBPop"/>
<!-- C端不感兴趣弹框 -->
 <dislike-c-pop wx:if="{{dislikeCPopShow}}" visible="{{dislikeCPopShow}}" dislikeList="{{dislikeList}}" conversation="{{conversation}}" sourcePage="2" bind:dislikeadd="onDislikeAdd" bind:close="onCloseDislikeCPop"/>