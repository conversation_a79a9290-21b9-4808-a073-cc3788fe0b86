page {
  background-color: #fff;
}

.hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

.body {
  padding: 32rpx;
  padding-bottom: 0;
  position: relative;
}

.input-box {
  position: relative;
  display: flex;
  align-items: center;
  .bottom-line();
}

.input-right {
  display: flex;
  align-items: center;
  .input-clear{
    opacity: 0.7;
    padding: 6rpx 24rpx;
    display: inline-flex;
  }
  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: @primary-color;
  }
  .num-err {
    color: @error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.input {
  font-size: 30rpx;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  display: block;
  height: 112rpx;
  display: flex;
  align-items: center;
  flex: 1;
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
}