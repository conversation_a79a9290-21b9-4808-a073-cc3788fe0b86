.p-header-txt {
  font-weight: bold;
  font-size: 34rpx;
  color: rgba(0, 146, 255, 1);
}

.p-sv-content {
  padding: 16rpx 24rpx;
}

.p-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 1);
  padding: 24rpx 32rpx 24rpx 48rpx;
}

.p-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pf-select {
  display: flex;
  align-items: center;
  margin-right: 48rpx;
}

.pf-img {
  width: 48rpx;
  height: 48rpx;
}

.pf-txt {
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
  margin-left: 8rpx;
}

.pf-btn {
  height: 96rpx;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 34rpx;
}
