.content {
  background: rgba(245, 246, 250, 1);
  padding: 24rpx;
  border-radius: 24rpx;
}

.placeholder {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
}

.tainput {
  font-size: 30rpx;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  height: 182rpx;
}

.info-num {
  width: 100%;
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 30rpx;
}

.num {
  color: @primary-color;
}
.num-err {
  color: @error-color;
}
.num-gray {
  color: rgba(0, 0, 0, 0.45);
}
