/*
 * @Date: 2022-01-29 14:15:22
 * @Description: IM消息栏
 */

import { VIP_ICON_B, VIP_ICON_C } from '@/config/app'

Component(class extends wx.$.Component {
  properties = {
    /** 组件用途类型 */
    pType: { type: String, value: 'common' },
    /** 父组件传的：IM消息的名字，头像，未读数量等 */
    affixData: { type: Object, value: {} },
    /** 红点对象 */
    redDotObjs: { type: Object, value: {} },
    /** 是否显示时间 */
    isShowTime: { type: Boolean, value: true },
    /** 移动的距离 */
    leftRange: { type: Number, value: 0 },
    /** 信息Id */
    InfoId: { type: String, value: '' },
    /** 是否展示title */
    showTitle: { type: Boolean, value: false },
    /** 是否展示完整的title */
    showFullTitle: { type: Boolean, value: false },
  }

  observers = {
    affixData(v) {
      const { showTitle } = this.data
      const { title, toUserRemark, toUserName, name } = v || {}
      this.setData({ name: showTitle ? title : (toUserRemark || toUserName || name) })
    },
  }

  data = {
    VIP_ICON_B,
    VIP_ICON_C,
    name: '',
  }

  onClick(e) {
    const { path, affixdata, ptype } = e.currentTarget.dataset
    let url = path
    if (ptype == 'im') {
      url = '/subpackage/tim/groupConversation/index'
    }
    this.triggerEvent('click', { path: url, item: affixdata, pType: ptype })
  }

  onClickS(element) {
    const { item, ptype } = element.currentTarget.dataset
    if (ptype == 'im') {
      this.triggerEvent('touchS', { element, item })
    }
  }

  onClickM(e) {
    const { ptype } = e.currentTarget.dataset
    if (ptype == 'im') {
      this.triggerEvent('touchM', e)
    }
  }

  onClickE(e) {
    const { ptype } = e.currentTarget.dataset
    if (ptype == 'im') {
      this.triggerEvent('touchE', e)
    }
  }

  // 置顶&取消置顶
  handleTop(e) {
    const { timchatid, ispinned } = e.currentTarget.dataset
    this.triggerEvent('handleTop', { conversationID: timchatid, isPinned: ispinned })
  }

  // 删除
  handleDel(e) {
    const { timchatid } = e.currentTarget.dataset
    this.triggerEvent('handleDel', { conversationID: timchatid })
  }
})
