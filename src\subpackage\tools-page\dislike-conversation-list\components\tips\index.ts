import { actions, dispatch } from '@/store/index'

Component(class extends wx.$.Component {
  useStore(state: StoreRootState) {
    const { isDislikeTipsShow1, isDislikeTipsShow2 } = state.storage
    const role = state.storage.userChooseRole
    return {
      role,
      isDislikeTipsShow: role == 1 ? isDislikeTipsShow1 : isDislikeTipsShow2,
      desc: `只保留最近30天标记为${role == 1 ? '不合适的牛人' : '不感兴趣老板'}`,
    }
  }

  onCloseTips() {
    const { role } = this.data as DataTypes<typeof this>
    if (role == 1) {
      dispatch(actions.storageActions.setItem({ key: 'isDislikeTipsShow1', value: false }))
    } else {
      dispatch(actions.storageActions.setItem({ key: 'isDislikeTipsShow2', value: false }))
    }
    this.triggerEvent('close')
  }
})
