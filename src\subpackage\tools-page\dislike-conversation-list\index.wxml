<custom-header title="{{title}}" />

<view wx:if="{{isRequest && imChatList.length == 0}}" class="empty">
    <image class="empty-img" src="https://cdn.yupaowang.com/yupao_app/videonodata3x.png" />
    <view class="empty-txt">{{emptyTxt}}</view>
</view>
<block wx:else>
  <tips id="tips" bind:close="onTipsClose" />
  <scroll-view 
    class="sc-view" 
    style="height:calc(100vh - {{topHeight + tipsHeight}}px)" 
    bindrefresherrefresh="onRefresh"
    refresher-enabled="{{isLogin}}"
    refresher-triggered="{{refreshing}}"
    scroll-y
    bindscrolltolower="onLoadMore"
    scroll-top="{{scrollTop}}"
    enhanced
    bounces="{{false}}"
    refresher-two-level-threshold="{{200}}"
  >
    <view class="body">
      <view wx:if="{{imChatList.length > 0}}">
        <block wx:for="{{imChatList}}" wx:key="conversationID" >
          <message-box wx:if="{{myMsgGroupOjb[item.conversationID]}}" pType="im" data="{{item}}"  affixData="{{myMsgGroupOjb[item.conversationID]}}" bind:click="onClickCon" bind:touchS="touchS" bind:touchE="touchE" bind:touchM="touchM" leftRange="{{leftRange}}" startX="{{startX}}" moveX="{{moveX}}" InfoId="{{InfoId}}" bind:handleTop="handleToMove" bind:handleDel="handleToDel" />
        </block>
      </view>
      <loading-refresh wx:if="{{loading && !refreshing}}" />
      <m-stripes />
    </view>
  </scroll-view>
</block>