page {
  background-color: rgba(255, 255, 255, 1);
}

.body {
  padding: 0 32rpx;
}

.input-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24rpx;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
  padding: 32rpx 0;
}

.input {
  width: 100%;
  height: 48rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
}
.ml32 {
  margin-left: 32rpx;
}

.mr32 {
  margin-right: 32rpx;
}

.placeholder {
  color: rgba(0, 0, 0, 0.25);
  font-size: 34rpx;
}

.yzm-v {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 228rpx;
}

.yzm-btn {
  color: rgba(0, 146, 255, 1);
  font-size: 34rpx;
  flex-shrink: 0;
}

.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 24rpx 32rpx;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 24rpx;
  border-radius: 16rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 34rpx;
}

.dis-btn {
  background: rgba(153, 211, 255, 1);
  color: rgba(255, 255, 255, 0.45);
}

.g-code {
  color: rgba(0, 0, 0, 0.25);
}
