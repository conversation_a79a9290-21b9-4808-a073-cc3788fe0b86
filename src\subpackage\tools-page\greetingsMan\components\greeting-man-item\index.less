.greeting-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.greeting-v {
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 124rpx;
}

.greeting-cc-v{
  width: 100%;
}

.greeting-left {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin: 40rpx 0;
}
.greeting-right {
  display: flex;
  align-items: center;
}

.cws-btn{
  margin-left: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
}

.cws-edit-btn {
  font-weight: bold;
  background: rgba(0, 146, 255, 1);
}

.cws-del-btn {
  background: rgba(232, 54, 46, 1);
}

.greeting-content {
  display: flex;
  align-items: center;
  width: 472rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  line-height: 44rpx;
  word-break: break-word;
  .ellip(5);
}

.r-slt-txt {
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
}