.rel-job-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.rel-job-v {
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
  display: flex;
  align-items: center;
  width: 100%;
}

.rel-job-cc-v {
  width: 100%;
  padding: 24rpx 0;
}

.rel-job-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rel-job-title {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 26rpx;
}

.rjt-txt {
  max-width: 454rpx;
  .ellip();
}

.rel-job-left {
  margin-top: 8rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  width: 100%;
}
.rel-job-right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.cws-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 36rpx;
}

.cws-del-btn {
  background: rgba(232, 54, 46, 1);
}

.rel-job-content {
  display: flex;
  align-items: center;
  width: 472rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  line-height: 44rpx;
  word-break: break-word;
  .ellip(5);
}
