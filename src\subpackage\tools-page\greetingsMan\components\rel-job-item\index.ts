Component(class extends wx.$.Component {
  properties = {
    relJob: {
      type: Object,
      value: {},
    },
    // 是否可以左滑
    isMove: {
      type: Boolean,
      value: true,
    },
    // 是否需要收起移动距离
    isPack: {
      type: Boolean,
      value: false,
    },
  }

  observers = {
    isPack() {
      const { leftRange } = this.data
      if (leftRange > 0) {
        this.setData({ leftRange: 0 })
      }
    },
  }

  data = {
    /** 移动的距离 */
    leftRange: 0,
    startX: 0,
    moveX: 0,
    isCurMove: false,
  }

  async onEdit() {
    wx.$.collectEvent.event('greeting_message_edit')
    const { relJob } = this.data as DataTypes<typeof this>
    this.triggerEvent('edit', { item: relJob })
  }

  async onDel() {
    await wx.$.u.waitAsync(this, this.onDel, [], 1000)
    wx.$.collectEvent.event('greeting_message_delete')
    const { relJob } = this.data as DataTypes<typeof this>
    this.setData({ leftRange: 0, isCurMove: false, startX: 0, moveX: 0 })
    this.triggerEvent('del', { item: relJob })
  }

  onClickS(e) {
    const { isMove } = this.data as DataTypes<typeof this>
    if (!isMove) return
    // 更新初始值
    this.setData({ leftRange: 0 })
    // startX记录开始移动的位置
    if (e.touches.length === 1) {
      this.setData({ startX: e.touches[0].clientX })
    }
  }

  onItemLongPress() {
    const { leftRange, isMove } = this.data as DataTypes<typeof this>
    // 滑动时判断是否为初始值
    if (!isMove || leftRange === 80) {
      return
    }
    this.setData({ leftRange: 80, isCurMove: true })
  }

  onClickM(e) {
    const { isMove } = this.data as DataTypes<typeof this>
    if (!isMove) return
    // 滑动时判断是否为初始值
    if (this.data.leftRange === 80) {
      return
    }
    if (e.touches.length === 1) {
      // 手指移动时水平方向位置
      this.setData({ moveX: e.touches[0].clientX })
      // 判断移动的距离是变大变小 变大便是右移
      if (this.data.startX < this.data.moveX) {
        // 更改移动时的距离。防止弹出删除按钮
        this.setData({ moveX: 0 })
      } else if (this.data.startX - this.data.moveX > 80) {
        this.setData({ leftRange: 80, isCurMove: true })
      }
    }
  }

  onClickE() {
    const { isMove, leftRange } = this.data as DataTypes<typeof this>
    if (!isMove) return
    // 松开后刷新 滑动的距离
    this.setData({
      moveX: 0,
    })
    if (!leftRange) {
      setTimeout(() => {
        this.setData({ isCurMove: false })
      }, 100)
    }
  }
})
