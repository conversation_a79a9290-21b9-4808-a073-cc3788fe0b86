<view class="rel-job-item" bind:longpress="onItemLongPress"  bind:tap="onEdit">
    <view class="rel-job-v">
        <view class="rel-job-cc-v" style="margin-left: {{leftRange ? '-' + leftRange  +'rpx': '' }}" bindtouchstart="onClickS" bindtouchmove="onClickM" bindtouchend="onClickE">
          <view class="rel-job-top">
            <view class="rel-job-title"><view class="rjt-txt">{{relJob.occ}}</view>：</view>
            <icon-font type="yp-icon_edit_grzl" size="32rpx" color="rgba(0, 0, 0, 0.65)" />
          </view>
          <view class="rel-job-left">
              <view class="rel-job-content">{{relJob.content}}</view>
          </view>
        </view>
        <view wx:if="{{leftRange}}" class="rel-job-right">
            <view class="cws-btn cws-del-btn" catch:tap="onDel">
                <icon-font type="yp-cws_del" size="40rpx" color="rgba(255, 255, 255, 1)" />
            </view>
        </view>
    </view>
</view>