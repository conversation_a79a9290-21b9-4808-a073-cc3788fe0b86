page {
  background-color: rgba(255, 255, 255, 1);
}

.page-v {
  display: flex;
  flex-direction: column;
}

.my-class {
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.top-v {
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0rpx 0rpx 24rpx rgba(0, 0, 0, 0.1);
  margin: 24rpx 32rpx 38rpx;
}

.head-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title-v {
  display: flex;
  align-items: center;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  margin-right: 8rpx;
}

.bubble-m {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  z-index: 35;
}

.icon-bubble-v {
  position: relative;
  display: flex;
  align-items: center;
}

.icon-bubble {
  position: absolute;
  top: 52rpx; /* 气泡与触发元素之间的距离 */
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.85);
  color: rgba(255, 255, 255, 1);
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  width: 438rpx;
  line-height: 150%;
  z-index: 40;
}

.icon-arrow {
  position: absolute;
  bottom: 100%; /* 箭头位置 */
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 12rpx solid rgba(0, 0, 0, 0.85); /* 与气泡背景颜色一致 */
}

.content {
  margin-top: 16rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 26rpx;
  word-break: break-word;
  .ellip(5);
}

.body-v {
  flex: 1;
  display: flex;
}

.sv-left {
  width: 158rpx;
  height: 100%;
  background: rgba(245, 246, 250, 1);
  flex-shrink: 0;
}

.l-item {
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 27rpx 24rpx;
  text-align: center;
}

.l-slt {
  background: rgba(255, 255, 255, 1);
  color: rgba(0, 146, 255, 1);
}

.sv-right {
  width: 100%;
}

.sv-content {
  padding: 0 40rpx;
}

.emp_v {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.emp_img {
  width: 200rpx;
  height: 200rpx;
}

.emp_add {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 264rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-size: 28rpx;
  border-radius: 40rpx;
  margin-top: 32rpx;
}

.hav-v {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hav_add {
  margin: 48rpx 0;
}

.reljob-top {
  padding: 0 16rpx;
}

.reljob-top-tips {
  padding: 24rpx;
  border-radius: 16rpx;
  background: rgba(245, 246, 250, 1);
}

.rt-tips-title {
  color: rgba(0, 0, 0, 0.65);
  font-size: 26rpx;
}

.rt-tips-content {
  margin-top: 8rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 26rpx;
  .ellip(5);
}

.reljob-body {
  padding: 0 40rpx;
}

.empty-img{
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.reljob-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  padding: 48rpx 0;
}

.reljob-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 320rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-size: 28rpx;
}
