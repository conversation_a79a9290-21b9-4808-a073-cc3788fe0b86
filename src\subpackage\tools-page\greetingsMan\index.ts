const { top: gTop, height: gHeight } = wx.$.u.sInfo().menuRect

let timer
Page(class extends wx.$.Page {
  useStore(state: StoreRootState) {
    return {
      role: state.storage.userChooseRole,
    }
  }

  data = {
    headHeight: gTop + gHeight + 4,
    // 顶部内容高度
    topHeight: 0,
    msHeight: 0,
    rtiHeight: 0,
    // 系统招呼语
    systemList: [],
    // 自定义招呼语
    customList: [],
    // 职位关联的招呼语
    relationJobList: [],
    // 是否开启自动招呼
    enable: false,
    // 系统招呼语id
    systemId: 0,
    // 自定义招呼语id
    customId: 0,
    // 顶部显示内容
    content: '',
    // 左边列表
    leftList: [
      { id: 'system', content: '推荐' },
      { id: 'custom', content: '自定义' },
      { id: 'reljob', content: '按职位设置', role: 1 },
    ],
    leftSltId: 'system',
    // 右边列表
    rightList: [],
    rightSltId: '',
    scrollIntoViewrollId: '',
    isShowBubble: false,
    isRequest: false,
  }

  operPath = '/subpackage/tools-page/greetingsOper/index'

  onLoad() {
    this.init()
  }

  init() {
    this.setData({ isRequest: false })
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/query']({ type: 1 }).then((res) => {
      const { code, data, message, error } = res || {}
      const { systemList, customList, enable, systemId, customId, relationJobList } = data || {}
      if (code == 0) {
        const sData: any = { systemList, customList, enable, systemId, customId, relationJobList }
        if (systemId) {
          sData.leftSltId = 'system'
          sData.rightList = systemList.map(it => ({ ...it, ky: `system_${it.id}` }))
          sData.rightSltId = systemId
          const system = systemList.find(it => it.id == systemId)
          if (system) {
            sData.content = system.content
          }
        } else if (customId) {
          sData.leftSltId = 'custom'
          sData.rightList = customList.map(it => ({ ...it, ky: `custom_${it.id}` }))
          sData.rightSltId = customId
          const custom = customList.find(it => it.id == customId)
          if (custom) {
            sData.content = custom.content
          }
        }
        this.setData(sData)
        this.scrollToView(sData.rightSltId, sData.leftSltId)
        setTimeout(() => {
          this.getChatTopHeightOnly()
        }, 50)
      }
      if (error && message != '请求成功') {
        wx.$.msg(message)
      }
    }).finally(() => {
      wx.hideLoading()
      this.setData({ isRequest: true })
    })
  }

  onLeftSelect(e) {
    const { id } = e.currentTarget.dataset
    const { systemList, customList, rightSltId, leftSltId } = this.data
    if (id == leftSltId) {
      return
    }
    const sData: any = { leftSltId: id }
    sData.rightList = (id == 'custom' ? customList : systemList).map(it => ({ ...it, ky: `${id}_${it.id}` }))
    this.setData(sData)
    const idx = sData.rightList.findIndex(it => it.id == rightSltId)
    if (idx >= 0) {
      this.scrollToView(rightSltId, sData.leftSltId)
    } else if (wx.$.u.isArrayVal(sData.rightList)) {
      this.setData({ scrollIntoViewrollId: 'top-view' })
      setTimeout(() => {
        this.setData({ scrollIntoViewrollId: '' })
      }, 50)
    }
    if (id == 'reljob') {
      wx.createSelectorQuery()
        .select('#reljob-top-id')
        .boundingClientRect((rect) => {
        // 使页面滚动到底部
          this.setData({ rtiHeight: rect?.height || 0 })
        })
        .exec()
    }
  }

  onSwitchClick() {
    const { enable } = this.data
    wx.showLoading({ title: '请求中', mask: true })
    wx.$.javafetch['POST/reach/v1/config/switch/report']({ imAutoHelloStatus: !enable }).then((res) => {
      wx.hideLoading()
      const { code, message, error } = res || {}
      if (code == 0) {
        this.setData({ enable: !enable })
        return
      }
      if (error && message != '请求成功') {
        wx.$.msg(message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  async onRelJobClick(e) {
    await wx.$.u.waitAsync(this, this.onRelJobClick, [e], 1000)
    const { item } = e.detail
    const { relationJobList } = this.data
    let nRelationJobList = [...(relationJobList || [])]
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/bossJobList']({ type: 1 }).then((res) => {
      wx.hideLoading()
      const { data } = res || {}
      const { jobList } = data || {}
      if (!wx.$.u.isArrayVal(jobList)) {
        wx.$.msg('当前无在线职位，请发布职位后设置')
        return
      }
      const nJobList = []
      const nYsJobList = []
      jobList.forEach((job) => {
        const { jobId } = job || {}
        const relJob = nRelationJobList.find(relJob => relJob.fromJobId == jobId)
        if (relJob) {
          const { content, id } = relJob
          nYsJobList.push({ ...job, content, gid: id })
        } else {
          nJobList.push(job)
        }
      })
      const allJobList = [...nJobList, ...nYsJobList]
      let job = allJobList[0]
      const { fromJobId } = item || {}
      const idx = jobList.findIndex(it => it.jobId == fromJobId)
      if (idx >= 0) {
        job = allJobList.find(job => job.jobId == fromJobId)
      } else {
        nRelationJobList = nRelationJobList.filter(relJob => relJob.fromJobId != fromJobId)
        this.setData({ relationJobList: nRelationJobList })
      }

      wx.$.nav.push(
        '/subpackage/tools-page/rel-job-oper/index',
        {},
        async (res) => {
          const nnRelationJobList = [...(nRelationJobList || [])]
          const { data } = res || {}
          const { jobId, gid, content, jobOccName } = data || {}
          const relJobIdx = nnRelationJobList.findIndex(relJob => relJob.fromJobId == jobId)
          if (relJobIdx >= 0) {
            nnRelationJobList[relJobIdx] = { ...nnRelationJobList[relJobIdx], content }
          } else {
            nnRelationJobList.push({ fromJobId: jobId, content, occ: jobOccName, id: gid })
          }
          this.setData({ relationJobList: nnRelationJobList })
        },
        {
          jobList: allJobList,
          job,
          title: '职位打招呼语',
        },
      )
    }).catch(() => {
      wx.hideLoading()
    })
  }

  onClick(e) {
    const { leftSltId } = this.data
    const { item } = e.detail
    const { id, content } = item || {}
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/choose']({ id, isSystem: leftSltId == 'system', type: 1 }).then((res) => {
      wx.hideLoading()
      const { code, error, message } = res || {}
      if (code == 0) {
        wx.$.msg('设置成功')
        setTimeout(() => {
          this.getChatTopHeightOnly()
        }, 50)
        this.setData({ rightSltId: id, content })
        return
      }
      if (!error && message != '请求成功') {
        wx.$.msg(message)
      }
    }).catch(() => {
      wx.hideLoading()
    })
  }

  async onAdd() {
    await wx.$.u.waitAsync(this, this.onAdd, [], 1000)
    const { rightList, customList } = this.data
    wx.$.nav.push(
      this.operPath,
      {},
      async (res) => {
        const { data } = res || {}
        const { id, content } = data || {}
        if (id) {
          const nList = wx.$.u.deepClone(rightList)
          nList.unshift({ id, content })
          const sData: any = { rightList: nList }
          const cnList = wx.$.u.deepClone(customList)
          cnList.unshift({ id, content })
          sData.customList = cnList
          this.setData(sData)
        }
      },
    )
  }

  onEdit(e) {
    const { item } = e.detail
    const { rightList, customList, rightSltId } = this.data
    const { id: eId, content: eContent } = item || {}
    wx.$.nav.push(
      this.operPath,
      {},
      async (res) => {
        const { data } = res || {}
        const { id, content } = data || {}
        const idx = rightList.findIndex((item) => item.id == id)
        if (idx >= 0) {
          const nList = wx.$.u.deepClone(rightList)
          const item = nList[idx]
          if (item) {
            nList[idx] = { ...item, content }
            const sData: any = { rightList: nList }
            if (rightSltId == id) {
              sData.content = content
            }
            this.setData(sData)
          }
        }

        const cidx = customList.findIndex((item) => item.id == id)
        if (cidx >= 0) {
          const nList = wx.$.u.deepClone(customList)
          const item = nList[idx]
          if (item) {
            nList[idx] = { ...item, content }
            this.setData({ customList: nList })
          }
        }
      },
      {
        title: '编辑打招呼语',
        id: eId,
        content: eContent,
        type: 2,
      },
    )
  }

  onDel(e) {
    const { item } = e.detail
    const { id, fromJobId } = item || {}
    if (id) {
      wx.showLoading({ title: '请求中...' })
      wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/delete']({ id }).then((res) => {
        wx.hideLoading()
        const { code, error, message } = res || {}
        if (code == 0) {
          const { rightList, rightSltId, customList, systemList, relationJobList } = this.data
          if (fromJobId && fromJobId != '0') {
            const nRelationJobList = relationJobList.filter((item) => item.fromJobId != fromJobId)
            this.setData({ relationJobList: nRelationJobList })
            return
          }
          const nList = wx.$.u.deepClone(rightList)
          const idx = nList.findIndex((item) => item.id == id)
          if (idx >= 0) {
            nList.splice(idx, 1)
            const sData: any = { rightList: nList }
            if (id == rightSltId) {
              const system = systemList[0]
              sData.content = system.content
              sData.rightSltId = system.id
            }
            this.setData(sData)
          }

          const cnList = wx.$.u.deepClone(customList)
          const cidx = cnList.findIndex((item) => item.id == id)
          if (cidx >= 0) {
            cnList.splice(idx, 1)
            this.setData({ customList: cnList })
          }
          return
        }
        if (error && message != '请求成功') {
          wx.$.msg(message)
        }
      }).catch(() => {
        wx.hideLoading()
      })
    }
  }

  /** 点击返回按钮逻辑 */
  onNavBack() {
    wx.$.r.back()
  }

  /** 是否显示提示气泡 */
  onShowBubble() {
    const { isShowBubble } = this.data as DataTypes<typeof this>
    if (isShowBubble) return
    this.setData({ isShowBubble: true })
    timer = setTimeout(() => {
      this.setData({ isShowBubble: false })
    }, 5000)
  }

  onHideBubble() {
    if (timer) {
      clearTimeout(timer)
    }
    this.setData({ isShowBubble: false })
  }

  /** 动态计算scroll-view高度 */
  getChatTopHeightOnly() {
    wx.createSelectorQuery()
      .select('#top-v')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ topHeight: rect?.height || 0 })
      })
      .exec()
    wx.createSelectorQuery()
      .select('#m-stripes')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ msHeight: rect?.height || 0 })
      })
      .exec()
  }

  // 滚动到指定位置
  scrollToView(id, type) {
    setTimeout(() => {
      this.setData({ scrollIntoViewrollId: `${type}_${id}` })
      setTimeout(() => {
        this.setData({ scrollIntoViewrollId: '' })
      }, 100)
    }, 100)
  }
})
