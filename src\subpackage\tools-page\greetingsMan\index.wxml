<custom-header my-class="my-class" customBack bind:back="onNavBack" title="打招呼语" />
<view class="page-v" style="height:calc(100vh - {{headHeight}}px)">
    <view id="top-v">
    <view class="top-v" >
        <view class="head-item">
            <view class="title-v">
                <view class="title">开聊时自动发送招呼语</view>
                <view class="icon-bubble-v">
                    <icon-font catch:tap="onShowBubble" type="yp-gt_question_mark" size="32rpx" color="rgba(0, 0, 0, 0.45)" />
                    <block wx:if="{{isShowBubble}}">
                        <view class="icon-bubble">
                            选中的招呼语将在每次开聊时自动发送
                            <view class="icon-arrow"></view>
                        </view>
                        <view  class="bubble-m" catch:tap="onHideBubble"></view>
                    </block>
                </view>
            </view>
            <m-switch style="display:flex;" active="{{enable}}" catch:click="onSwitchClick" />
        </view>
        <view class="content">{{content}}</view>
    </view>
    </view>
    <view class="body-v">
        <scroll-view class="sv-left" scroll-y="{{true}}">
            <block wx:for="{{leftList}}" wx:key="id">
                <view wx:if="{{!item.role || item.role == role}}" catch:tap="onLeftSelect"  class="l-item {{item.id == leftSltId ? 'l-slt' : ''}}" data-id="{{item.id}}">{{item.content}}</view>
            </block> 
        </scroll-view>
        <scroll-view 
            class="sv-right"
            scroll-y="{{true}}"
            style="height:calc(100vh - {{headHeight + topHeight + msHeight}}px)"
            enhanced="{{true}}"
            show-scrollbar="{{false}}"
            scroll-into-view="{{scrollIntoViewrollId}}"
        >
            <block wx:if="{{leftSltId == 'reljob'}}">
                <view id="reljob-top-id" class="reljob-top">
                    <view class="reljob-top-tips">
                        <view class="rt-tips-title">未按职位单独设置时，将使用：</view>
                        <view  class="rt-tips-content">{{content}}</view>
                    </view>
                </view>
                <view wx:if="{{relationJobList.length > 0}}" class="reljob-body">
                    <block wx:for="{{relationJobList}}" wx:key="id">
                        <rel-job-item 
                            bind:edit="onRelJobClick"
                            bind:del="onDel"
                            relJob="{{item}}" 
                        />
                    </block>
                </view>
                <view class="reljob-footer" style="{{relationJobList.length == 0 ? 'height:calc(100% - '+ rtiHeight  +'px);padding:0 !important' : ''}}">
                    <image wx:if="{{relationJobList.length == 0}}"  class="empty-img" src="https://cdn.yupaowang.com/yupao_common/fe202921.png" />
                    <view class="reljob-btn" catch:tap="onRelJobClick">按职位添加招呼语</view>
                </view>
            </block>
            <block wx:elif="{{isRequest}}">
                <view id="top-view"></view>
                <view wx:if="{{rightList.length > 0}}" class="sv-content">
                    <block wx:for="{{rightList}}" wx:key="ky">
                        <view id="{{leftSltId}}_{{item.id}}">
                            <greeting-man-item 
                                catch:click="onClick" 
                                catch:edit="onEdit"
                                catch:del="onDel"
                                greeting="{{item}}" 
                                sltedId="{{rightSltId}}" 
                                isMove="{{leftSltId == 'custom'}}" 
                            />
                        </view>
                    </block>
                    <view class="hav-v" wx:if="{{leftSltId == 'custom' && rightList.length < 10}}">
                        <view  class="emp_add hav_add" catch:tap="onAdd">添加打招呼语</view>
                    </view>
                </view>
                <view class="emp_v" wx:else>
                    <image class="emp_img" src="https://cdn.yupaowang.com/yp_mini/images/xjj/yp_min_greetings_empty.png" />
                    <view class="emp_add" catch:tap="onAdd">添加打招呼语</view>
                </view>
            </block>
        </scroll-view>
    </view>
    <m-stripes id="m-stripes" />
</view>