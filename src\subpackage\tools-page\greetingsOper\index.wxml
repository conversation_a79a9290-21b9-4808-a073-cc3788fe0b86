<custom-header customBack bind:back="onNavBack" title="{{title}}" />
<view class="body">
  <textarea
    class="textarea"
    style="height: calc(100vh - 24rpx - {{footerHeight + topHeight + bottomHeight}}px);"
    placeholder="{{placeholder}}"
    placeholder-class="placeholder"
    value="{{content}}"
    maxlength="-1"
    adjust-position="{{false}}"
    disable-default-padding
    focus="{{isFocus}}"
    bind:focus="onFocus"
    bind:blur="onHideKey"
    bind:keyboardheightchange="onHeightChange"
    bind:input="onInput">
  </textarea>
</view>
<view id="footer" class="footer {{bottomHeight > 0 ? 'default-pb' : ''}}" style="bottom: {{bottomHeight || 0}}px;">
  <view class="info-text">
    <view></view>
    <view class="info-num">
      <view class="{{content.length > maxContent ? 'num-err' : 'num'}} {{content.length < 1 && 'num-gray'}}">{{content.length || 0}}</view>
      <view class="num-gray">/{{maxContent}}</view>
      <view class="btn {{!content ? 'dbt' : ''}}" bind:tap="onSave">保存</view>
    </view>
  </view>
</view>
