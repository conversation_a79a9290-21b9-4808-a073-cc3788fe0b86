.sp-body {
  position: relative;
  background: rgba(255, 255, 255, 1);
  border-radius: 24rpx 24rpx 0rpx 0rpx;
}

.sp-head {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.sp-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sp-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
}

.sp-scroll {
  width: 100%;
}

.sp-height-nodesc {
  max-height: 954rpx;
  min-height: 528rpx;
}

.sp-item-v {
  width: 100%;
  padding: 0 32rpx;
}

.sp-item-content {
  padding: 32rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.sp-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sp-item-title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  line-height: 42rpx;
  .ellip();
}

.sp-salary {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  line-height: 42rpx;
  flex-shrink: 0;
}

.sp-fl {
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.sp-width-slted {
  width: calc(100% - 48rpx);
}

.selected {
  color: rgba(0, 146, 255, 1);
}

.sp-yset {
  color: rgba(0, 0, 0, 0.25);
  font-size: 30rpx;
}

.sp-fs {
  margin-left: 48rpx;
  flex-shrink: 0;
}

.sp-line {
  margin: 0 16rpx;
  flex-shrink: 0;
}
