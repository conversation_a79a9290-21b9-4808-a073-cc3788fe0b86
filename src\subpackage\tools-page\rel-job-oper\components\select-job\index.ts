/*
 * @Date: 2022-03-15 15:06:27
 * @Description: 更换职位
 */

Component(class extends wx.$.Component {
  properties = {
    list: { type: Array, value: [] },
    visible: { type: Boolean, value: false },
    title: { type: String, value: '选择职位' },
    value: { type: Number, value: 0 },
  }

  observers = {
    visible(v) {
      if (v) {
        this.init()
      } else {
        this.setData({ job: {} })
      }
    },
  }

  data = {
    selectedId: 0,
    jobList: [],
    job: {},
  }

  init() {
    const { value, list } = this.data as DataTypes<typeof this>
    const jobList = []
    const sData:any = { selectedId: value }
    if (wx.$.u.isArrayVal(list)) {
      list.forEach(job => {
        const { jobOccName, jobCityName, jobSalary } = job || {}
        const btmLabels = [jobOccName, jobCityName, jobSalary]
        if (job.jobId == value) {
          sData.job = job
        }
        jobList.push({ ...job, btmLabels })
      })
    }
    sData.jobList = jobList
    this.setData(sData)
  }

  onClosePop() {
    this.setData({ job: {} })
    this.triggerEvent('close')
  }

  onConfirm(e) {
    const { item } = e.currentTarget.dataset
    const { jobId: nJobId } = item || {}
    const { job } = this.data as DataTypes<typeof this>
    const { jobId } = job || {} as any
    if (!nJobId) {
      wx.$.msg('请选择职位信息')
      return
    }
    if (nJobId === jobId) {
      return
    }
    this.triggerEvent('confirm', { job: item })
  }

  onTouchMove() { }
})
