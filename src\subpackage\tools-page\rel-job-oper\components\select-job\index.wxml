<drawer visible="{{visible}}" catch:close="onClosePop" isMaskClose catch:touchmove="onTouchMove">
    <view class="sp-body">
        <view class="sp-head">
            <view class="sp-top">
                <view class="sp-title">{{title}}</view>
                <icon-font type="yp-icon_pop_close" size="48rpx" color="rgba(0, 0, 0, 0.25)" catch:tap="onClosePop" />
            </view>
        </view>
        <scroll-view class="sp-scroll sp-height-nodesc" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
            <view class="sp-item-v" wx:for="{{jobList}}" wx:key="jobId" data-item="{{item}}" catch:tap="onConfirm">
                <view class="sp-item-content">
                    <view class="sp-item">
                        <view class="sp-fl {{item.jobId == selectedId ? 'sp-width-slted' : ''}}">
                            <view class="sp-item-title {{item.jobId == selectedId ? 'selected' : ''}} {{item.jobId != selectedId && item.gid ? 'sp-yset' : ''}}">
                                {{item.jobOccName}}
                            </view>
                            <view wx:if="{{item.jobSalary}}" class="sp-salary  {{item.jobId == selectedId ? 'selected' : ''}} {{item.jobId != selectedId && item.gid ? 'sp-yset' : ''}}">
                               <text class="sp-line">|</text>{{item.jobSalary}}
                            </view>
                        </view>
                        <icon-font type="yp-cls_g" size="32rpx" color="rgba(0, 146, 255, 1)" wx:if="{{item.jobId == selectedId}}" />
                        <view wx:elif="{{item.gid}}" class="sp-yset sp-fs">已设置</view>
                    </view>
                </view>
            </view>
        </scroll-view>
        <m-stripes />
    </view>
</drawer>