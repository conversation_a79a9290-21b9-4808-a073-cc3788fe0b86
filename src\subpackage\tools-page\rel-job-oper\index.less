page {
  background-color: #fff;
}

.body {
  padding: 0 32rpx 0;
}

.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 27rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
  margin-bottom: 16rpx;
  width: 686rpx;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  padding-right: 48rpx;
  flex-shrink: 0;
}

.jon-info {
  display: flex;
  align-items: center;
}

.info-job-title {
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
  max-width: 207px;
  .ellip();
}

.info-job-salary {
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
  flex-shrink: 0;
}

.sp-line {
  margin: 0 16rpx;
  flex-shrink: 0;
}

.info-icon {
  flex-shrink: 0;
}

.textarea {
  font-size: 30rpx;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  display: block;
  word-break: break-word;
  white-space: pre-wrap;
  line-height: 50rpx;
}

.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.45);
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx;
  background-color: #fff;
  .safe-area(24rpx);
  border-top: 1rpx solid rgba(233, 237, 243, 1);
  &.default-pb {
    padding-bottom: 24rpx;
  }
}

.info-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;

  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: @primary-color;
  }
  .num-err {
    color: @error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}

.btn {
  width: 96rpx;
  height: 56rpx;
  border-radius: 8rpx;
  border: 2rpx solid rgba(0, 146, 255, 1);
  color: rgba(0, 146, 255, 1);
  font-weight: bold;
  font-size: 26rpx;
  margin-left: 20rpx;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
}

.dbt {
  border: 2rpx solid rgba(0, 0, 0, 0.25);
  color: rgba(0, 0, 0, 0.25);
}
