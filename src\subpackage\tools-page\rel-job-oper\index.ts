import { dealDialogByApi } from '@/utils/helper/dialog/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'

/**
 * @description: 常用语添加和修改
 */
const { top: topHeight, height } = getMenuButtonBoundingClientRect()

Page(class extends wx.$.Page {
  data = {
    // 导航头部高度
    topHeight: topHeight + height,
    // 底部悬浮按钮高度
    footerHeight: 0,
    headHeight: 0,
    info: {},
    maxContent: 100,
    /** 旧的输入框的值 */
    jobOld: {} as any,
    /** 是否获取了焦点 */
    isFocus: false,
    /** 键盘高度 */
    bottomHeight: 0,
    /** 标题 */
    title: '添加打招呼语',
    /** */
    placeholder: '输入你的招呼语，请不要填写手机号、QQ、微信等联系方式或广告信息，否则系统将封禁你的账号',
    /** 请求接口类型:话术类型（1招呼语，2常用语，3自动回复，4自动询问） */
    rqType: 1,
    /** 在职职位列表数据 */
    jobList: [],
    /** 显示的职位 */
    job: {} as any,
    isSelectJobShow: false,
  }

  onLoad() {
    const { maxContent, title, type, jobList, job } = wx.$.nav.getDataPK(this)
    const sData: any = {
      job: job || {},
      jobList,
      jobOld: job || {},
      maxContent: maxContent || 100,
      bottomHeight: 0,
    }
    if (title) {
      sData.title = title
    }
    if (type) {
      sData.type = type
    }
    this.setData(sData)
    setTimeout(() => {
      this.onGetFooterHeight()
    }, 100)
  }

  onReady() {
    this.setData({ isFocus: true })
  }

  onHeightChange(e) {
    this.setData({
      bottomHeight: e.detail.height || 0,
    })
  }

  onGetFooterHeight() {
    wx.createSelectorQuery()
      .select('#footer')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ footerHeight: rect?.height || 0 })
      })
      .exec()

    wx.createSelectorQuery()
      .select('#head')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ headHeight: rect?.height || 0 })
      })
      .exec()
  }

  /** 点击提交按钮 */
  async onSave() {
    await wx.$.u.waitAsync(this, this.onSave, [], 500)
    if (!this.saveBool()) {
      return
    }
    wx.$.collectEvent.event('greeting_message_save', { button_name: '保存' })
    this.onHideKey()
    // wx.$.msg('保存成功')
    // 提交数据
    this.submitRequest()
  }

  submitRequest() {
    const { job } = this.data
    if (job.gid) {
      this.edit()
    } else {
      this.add()
    }
  }

  add() {
    const { rqType, job } = this.data
    const { jobId, content } = job || {}
    const ncontent = `${content}`.trim()
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/add']({ content: ncontent, type: rqType, fromJobId: jobId }).then(async (res) => {
      wx.hideLoading()
      const { code, data, error, message } = res
      if (code == 0) {
        const { content, id } = data || {}
        wx.$.nav.event({ data: { ...job, content, gid: id } })
        wx.$.nav.back()
        return
      }
      if (error && message) {
        wx.$.alert({ content: message })
      }
    }).catch((err) => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '添加失败,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }

  edit() {
    const { job } = this.data
    const { content, gid } = job || {}
    const ncontent = `${content}`.trim()
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/update']({ id: gid, content: ncontent }).then(async (res) => {
      wx.hideLoading()
      const { code, error, message } = res
      if (code == 0) {
        wx.$.nav.event({ data: { ...job, content, gid } })
        wx.$.nav.back()
        return
      }
      if (error && message) {
        wx.$.alert({ content: message })
      }
    }).catch((err) => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '修改失败,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }

  /** 判断输入的内容是否有效 */
  saveBool() {
    const { job, maxContent, jobOld } = this.data
    if (!job.content) {
      wx.$.msg('您还没有输入')
      return false
    }

    if (job.content === jobOld.content && job.jobId === jobOld.jobId) {
      wx.$.r.back()
      return false
    }
    if (job.content.length > maxContent) {
      wx.$.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  /** 点击返回按钮逻辑 */
  async onNavBack() {
    this.onHideKey()
    const { job, jobOld } = this.data
    if (job.content === jobOld.content && job.jobId === jobOld.jobId) {
      wx.$.r.back()
      return
    }
    if (!job.content || `${job.content}`.trim() == '') {
      wx.$.r.back()
      return
    }
    const pageCode = getPageCode()
    const popup = await dealDialogByApi('zhaohuxiugai', pageCode)
    console.log('popup:', popup)
    if (popup) {
      wx.$.showModal({
        ...popup,
        pageCode,
        success: (res) => {
          const { jumpEventType } = res || {}
          if (jumpEventType == 4) {
            wx.$.r.back()
          }
        },
      })
    } else {
      wx.$.r.back()
    }
  }

  onFocus(e) {
    this.setData({
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    })
    this.onGetFooterHeight()
  }

  /** 收起键盘 */
  onHideKey() {
    this.setData({ isFocus: false, bottomHeight: 0 })
  }

  onInput(e) {
    const { job } = this.data
    this.setData({ job: { ...job, content: e.detail.value } })
  }

  onSelectJobShow() {
    this.setData({ isSelectJobShow: true })
  }

  onSelectJobClose() {
    this.setData({ isSelectJobShow: false })
  }

  onSelectJobConfirm(e) {
    const { job } = e.detail
    this.setData({ job, jobOld: job, isSelectJobShow: false })
  }
})
