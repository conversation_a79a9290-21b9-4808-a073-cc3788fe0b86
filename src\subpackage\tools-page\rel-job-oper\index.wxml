<custom-header customBack bind:back="onNavBack" title="{{title}}" />
<view class="body">
  <view id="head" class="head" bind:tap="onSelectJobShow">
      <view class="title">职位</view>
      <view class="jon-info">
          <view class="info-job-title">{{job.jobOccName}}</view>
          <view class="info-job-salary" wx:if="{{job.jobSalary}}"><text class="sp-line">|</text>{{job.jobSalary}}</view>
          <icon-font custom-class="info-icon" type="yp-msg_set_mianbaoxue" size="32rpx" color="rgba(0, 0, 0, 0.65)" />
      </view>
  </view>
  <textarea
    class="textarea"
    style="height: calc(100vh - 16rpx - {{footerHeight + topHeight + bottomHeight + headHeight}}px);"
    placeholder="{{placeholder}}"
    placeholder-class="placeholder"
    value="{{job.content}}"
    maxlength="-1"
    adjust-position="{{false}}"
    disable-default-padding
    focus="{{isFocus}}"
    bind:focus="onFocus"
    bind:blur="onHideKey"
    bind:keyboardheightchange="onHeightChange"
    bind:input="onInput">
  </textarea>
</view>
<view id="footer" class="footer {{bottomHeight > 0 ? 'default-pb' : ''}}" style="bottom: {{bottomHeight || 0}}px;">
  <view class="info-text">
    <view></view>
    <view class="info-num">
      <view class="{{job.content.length > maxContent ? 'num-err' : 'num'}} {{job.content.length < 1 && 'num-gray'}}">{{job.content.length || 0}}</view>
      <view class="num-gray">/{{maxContent}}</view>
      <view class="btn {{!job.content ? 'dbt' : ''}}" bind:tap="onSave">保存</view>
    </view>
  </view>
</view>
<select-job wx:if="{{isSelectJobShow}}" list="{{jobList}}" value="{{job.jobId}}" visible="{{isSelectJobShow}}" bind:confirm="onSelectJobConfirm" bind:close="onSelectJobClose"/>