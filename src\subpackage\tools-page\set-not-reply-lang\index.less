page {
  background: rgba(255, 255, 255, 1);
}

.snrl-body {
  padding: 0 32rpx;
}

.snrl-tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
}

.snrl-tab-txt {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
}

.snrl-no-data {
  padding-top: 16rpx;
}

.snrl-no-data-img {
  width: 100%;
  height: 432rpx;
}

.snrl-reply-title {
  padding: 24rpx 0;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
  border-top: 1rpx solid rgba(233, 237, 243, 1);
}

.snrl-reply-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.snrl-slt-img {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.snrl-reply-content {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  line-height: 160%;
  white-space: pre-wrap;  /* 保留换行符和空格 */
  word-break: break-all;  /* 强制任意位置断行 */
  overflow-wrap: anywhere; /* 智能换行 */
  hyphens: manual;        /* 手动控制连字符 */
}

.snrl-reply-slted {
  color: rgba(0, 146, 255, 1);
}

.snrl-reply-add {
  padding: 32rpx 0;
  color: rgba(0, 146, 255, 1);
  font-size: 30rpx;
}

.snrl-reply-edit {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.snrl-reply-zdy-v {
  flex: 1;
}

.snrl-reply-zdy {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
  width: 622rpx;
}

.snrl-zdy-top {
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
}

.snrl-edit-btn {
  display: flex;
  align-items: center;
}

.snrl-edit-btn-txt {
  color: rgba(0, 146, 255, 1);
  font-size: 26rpx;
}
