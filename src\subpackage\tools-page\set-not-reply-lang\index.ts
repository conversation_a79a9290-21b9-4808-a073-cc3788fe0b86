Page(class extends wx.$.Page {
  data = {
    slted: 0,
    // 是否开启自动招呼
    enable: false,
    // 系统招呼语id
    systemId: 0,
    // 自定义招呼语id
    customId: 0,
    // 自定义招呼语
    customList: [],
    // 系统招呼语
    systemList: [],
  }

  onLoad() {
    const params = wx.$.r.getParams() || {}
    const { systemId, customId, enable, customList, systemList } = params || {}
    if (wx.$.u.isArrayVal(systemList)) {
      const sData:any = { enable, systemId, customId, customList, systemList }
      if (enable) {
        sData.slted = Number(customId || 0) || Number(systemId || 0)
      }
      this.setData(sData)
    } else {
      this.initData()
    }
  }

  initData() {
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/query']({ type: 5 }).then((res) => {
      const { data } = res || {}
      const { enable, systemId, customId, customList, systemList } = data || {}
      const sData:any = { enable, systemId, customId, customList, systemList }
      if (enable) {
        sData.slted = Number(customId || 0) || Number(systemId || 0)
      }
      this.setData(sData)
    })
  }

  onSwitchClick() {
    const { enable } = this.data
    wx.$.collectEvent.event('inappropriate_auto_reply_toggle_click', { toggle_type: !enable ? '开启' : '关闭' })
    wx.showLoading({ title: '请求中' })
    wx.$.javafetch['POST/reach/v1/config/switch/report']({ imAutoNotMatchReplyStatus: !enable }).then(() => {
      wx.hideLoading()
      if (!enable) {
        this.initData()
      }
      this.setData({ enable: !enable })
    }).catch(() => {
      wx.hideLoading()
    })
  }

  onSletChanged(e) {
    const { item, type } = e.currentTarget.dataset
    const { id } = item || {}
    const { slted } = this.data
    if (slted == id) {
      return
    }
    if (id) {
      wx.showLoading({ title: '请求中' })
      wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/choose']({ id, type: 5, isSystem: type == 'system' }).then(() => {
        wx.hideLoading()
        this.setData({ slted: id })
      }).catch(() => {
        wx.hideLoading()
      })
    }
  }

  async onAdd() {
    await wx.$.u.waitAsync(this, this.onAdd, [], 1000)
    wx.$.collectEvent.event('custom_reply_add_click', { button_name: '添加自定义回复语' })
    wx.$.nav.push(
      '/subpackage/tools-page/set-zdy-reply-lang/index',
      {},
      (res) => {
        const { data } = res || {}
        this.setData({ customList: [data] })
      },
      {
        maxContent: 200,
        type: 1,
        rqType: 5,
        title: ' ',
      },
    )
  }

  async onEdit() {
    await wx.$.u.waitAsync(this, this.onEdit, [], 1000)
    const { customList } = this.data
    let custom:any = {}
    if (wx.$.u.isArrayVal(customList)) {
      custom = { ...(customList[0] || {}) }
    }
    const { content, id } = custom || {}
    wx.$.nav.push(
      '/subpackage/tools-page/set-zdy-reply-lang/index',
      {},
      (res) => {
        const { data } = res || {}
        this.setData({ customList: [data] })
      },
      {
        id,
        content,
        maxContent: 200,
        type: 2,
        rqType: 5,
        title: ' ',
      },
    )
  }
})
