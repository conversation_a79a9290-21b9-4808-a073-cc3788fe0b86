<custom-header title="设置不合适回复语" />
<view class="snrl-body">
  <view class="snrl-tab">
    <view class="snrl-tab-txt">移入不合适时自动向对方发送回复语</view>
    <m-switch style="display:flex;" active="{{enable}}" catch:click="onSwitchClick" />
  </view>
  <view wx:if="{{!enable}}" class="snrl-no-data">
    <image class="snrl-no-data-img" src="https://cdn.yupaowang.com/yupao_common/762e03ba.png" />
  </view>
  <block wx:if="{{enable}}">
    <view class="snrl-reply-title">不合适回复语</view>
    <view class="snrl-reply-item" wx:for="{{systemList}}" data-item="{{item}}" data-type="system" bind:tap="onSletChanged" wx:key="index">
        <image wx:if="{{slted == item.id}}" class="snrl-slt-img" src="https://cdn.yupaowang.com/yupao_common/77ef0737.png" />
        <image wx:else class="snrl-slt-img" src="https://cdn.yupaowang.com/yupao_common/9773636c.png" />
        <view class="snrl-reply-content {{slted == item.id ? 'snrl-reply-slted' : ''}}">{{item.content}}</view>
    </view>
    <view wx:if="{{customList.length == 0}}" class="snrl-reply-add" bind:tap="onAdd">添加自定义回复语</view>
    <view wx:else class="snrl-reply-edit"  data-item="{{customList[0]}}" bind:tap="onSletChanged">
        <image wx:if="{{slted == customList[0].id}}" class="snrl-slt-img" src="https://cdn.yupaowang.com/yupao_common/77ef0737.png" />
        <image wx:else class="snrl-slt-img" src="https://cdn.yupaowang.com/yupao_common/9773636c.png" />
        <view class="snrl-reply-zdy-v">
          <view class="snrl-reply-zdy">
              <view class="snrl-zdy-top">自定义回复</view>
              <view class="snrl-edit-btn" catch:tap="onEdit">
                <icon-font type="yp-cws_edit" size="32rpx" color="rgba(0, 146, 255, 1)"/>
                <text class="snrl-edit-btn-txt">编辑</text>
              </view>
          </view>
          <view class="snrl-reply-content {{slted == customList[0].id ? 'snrl-reply-slted' : ''}}">{{customList[0].content}}</view>
        </view>
    </view>
  </block>
  <m-stripes />
</view>