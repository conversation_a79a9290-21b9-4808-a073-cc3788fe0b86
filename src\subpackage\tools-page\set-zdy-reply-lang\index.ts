import { dealDialogByApi } from '@/utils/helper/dialog/index'
import { getPageCode } from '@/utils/helper/resourceBit/index'
import { getMenuButtonBoundingClientRect } from '@/utils/tools/common/index'

/**
 * @description: 添加/修改不合适自动回复语
 */
const { top: topHeight, height } = getMenuButtonBoundingClientRect()

Page(class extends wx.$.Page {
  data = {
    // 导航头部高度
    topHeight: topHeight + height + 4,
    // 顶部标题
    headHeight: 0,
    // 底部悬浮按钮高度
    footerHeight: 0,
    info: {},
    maxContent: 100,
    /** 输入框的值 */
    content: '',
    /** 旧的输入框的值 */
    contentOld: '',
    /** 是否获取了焦点 */
    isFocus: false,
    /** 键盘高度 */
    bottomHeight: 0,
    /** 标题 */
    title: ' ',
    /** */
    placeholder: '输入你的不合适自动回复语，请不要填写手机号、QQ、微信等联系方式或广告信息，否则系统将封禁你的账号',
    /** 类型: 1.添加 2.修改 */
    type: 1,
    /** 请求接口类型:话术类型（1招呼语，2常用语，3自动回复，4自动询问，5不匹配自动回复） */
    rqType: 5,
    /** 修改常用语ID */
    id: 0,
  }

  onLoad() {
    const { content, maxContent, title, type, id } = wx.$.nav.getDataPK(this)
    const sData: any = {
      content,
      contentOld: content,
      maxContent: maxContent || 100,
      bottomHeight: 0,
    }
    if (title) {
      sData.title = title
    }
    if (type) {
      sData.type = type
    }
    if (id) {
      sData.id = id
    }
    this.setData(sData)
    setTimeout(() => {
      this.onGetFooterHeight()
    }, 100)
  }

  onReady() {
    this.setData({ isFocus: true })
  }

  onHeightChange(e) {
    this.setData({
      bottomHeight: e.detail.height || 0,
    })
  }

  onGetFooterHeight() {
    wx.createSelectorQuery()
      .select('#footer')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ footerHeight: rect?.height || 0 })
      })
      .exec()
    wx.createSelectorQuery()
      .select('#head')
      .boundingClientRect((rect) => {
        // 使页面滚动到底部
        this.setData({ headHeight: rect?.height || 0 })
      })
      .exec()
  }

  /** 点击提交按钮 */
  async onSave() {
    await wx.$.u.waitAsync(this, this.onSave, [], 500)
    if (!this.saveBool()) {
      return
    }
    this.onHideKey()
    // 提交数据
    this.submitRequest()
  }

  submitRequest() {
    const { type } = this.data
    if (type == 2) {
      this.edit()
    } else {
      this.add()
    }
  }

  add() {
    const { rqType } = this.data
    const content = `${this.data.content}`.trim()
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/add']({ content, type: rqType }).then(async (res) => {
      wx.hideLoading()
      const { code, data, error, message } = res
      if (code == 0) {
        wx.$.nav.event({ data })
        wx.$.nav.back()
        return
      }
      if (error && message) {
        wx.$.alert({ content: message })
      }
    }).catch((err) => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '添加失败,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }

  edit() {
    const { id } = this.data
    const content = `${this.data.content}`.trim()
    wx.showLoading({ title: '请求中...' })
    wx.$.javafetch['POST/reach/v2/im/userCustomSpeechManage/update']({ id, content }).then(async (res) => {
      wx.hideLoading()
      const { code, error, message } = res
      if (code == 0) {
        wx.$.nav.event({ data: { content, id } })
        wx.$.nav.back()
        return
      }
      if (error && message) {
        wx.$.alert({ content: message })
      }
    }).catch((err) => {
      wx.hideLoading()
      const { error, message } = err || {}
      let msg = '修改失败,请稍后重试'
      if (error && message) {
        msg = message
      }
      wx.$.msg(msg)
    })
  }

  /** 判断输入的内容是否有效 */
  saveBool() {
    const { content, maxContent, contentOld } = this.data
    if (!content) {
      wx.$.msg('你还没有输入')
      return false
    }

    if (content === contentOld) {
      wx.$.r.back()
      return false
    }
    if (content.length > maxContent) {
      wx.$.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  /** 点击返回按钮逻辑 */
  async onNavBack() {
    this.onHideKey()
    const { content, contentOld } = this.data
    if (content === contentOld) {
      wx.$.r.back()
      return
    }
    if (!this.data.content || `${this.data.content}`.trim() == '') {
      wx.$.r.back()
      return
    }
    const pageCode = getPageCode()
    const popup = await dealDialogByApi('buheshixiugai', pageCode)
    if (popup) {
      wx.$.showModal({
        ...popup,
        pageCode,
        success: (res) => {
          const { jumpEventType } = res || {}
          if (jumpEventType == 4) {
            wx.$.r.back()
          }
        },
      })
    } else {
      wx.$.r.back()
    }
  }

  onFocus(e) {
    this.setData({
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    })
    this.onGetFooterHeight()
  }

  /** 收起键盘 */
  onHideKey() {
    this.setData({ isFocus: false, bottomHeight: 0 })
  }

  onInput(e) {
    this.setData({ content: e.detail.value })
  }

  onClear() {
    this.setData({ content: '' })
  }
})
