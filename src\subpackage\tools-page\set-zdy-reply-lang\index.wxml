<custom-header customBack bind:back="onNavBack" title="{{title}}" />
<view class="body">
  <view id="head" class="head">
    <view class="title">自定义回复语</view>
    <view class="desc">以下内容将通过消息方式回复给牛人</view>
  </view>
  <textarea
    class="textarea"
    style="height: calc(100vh - 32rpx - {{footerHeight + topHeight + bottomHeight + headHeight}}px);"
    placeholder="{{placeholder}}"
    placeholder-class="placeholder"
    value="{{content}}"
    maxlength="-1"
    adjust-position="{{false}}"
    disable-default-padding
    focus="{{isFocus}}"
    bind:focus="onFocus"
    bind:blur="onHideKey"
    bind:keyboardheightchange="onHeightChange"
    bind:input="onInput">
  </textarea>
</view>
<view id="footer" class="footer {{bottomHeight > 0 ? 'default-pb' : ''}}" style="bottom: {{bottomHeight || 0}}px;">
  <view class="info-text">
    <view class="info-num">
      <view class="{{content.length > maxContent ? 'num-err' : 'num'}} {{content.length < 1 && 'num-gray'}}">{{content.length || 0}}</view>
      <view class="num-gray">/{{maxContent}}</view>
    <view class="clear {{content.length > 0 ? 'clear-h' : ''}}" bind:tap="onClear">清空内容</view>
    </view>
    <view class="btn {{!content ? 'dbt' : ''}}" bind:tap="onSave">完成</view>
  </view>
</view>
