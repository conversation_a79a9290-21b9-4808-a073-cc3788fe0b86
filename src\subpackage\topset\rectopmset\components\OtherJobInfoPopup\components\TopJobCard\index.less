.topJobCard {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.header {
  .flexRC();

  color: @text85;
  font-size: 34rpx;
  font-weight: 700;

  .stauts {
    .flexRCC();
    padding: 0 12rpx;
    height: 48rpx;
    border-radius: 8rpx;
    border: 2rpx solid #e0f3ff;
    margin-right: 16rpx;

    color: @primary-color;
    font-size: 30rpx;
    white-space: nowrap;
  }

  .title {
    .textrow(1);
    color: @text85;
    font-size: 32rpx;
    font-weight: 700;
    line-height: 44rpx;
  }
}

.desc {
  .textrow(2);

  color: @text85;
  font-size: 30rpx;
  line-height: 48rpx;
  margin-top: 8rpx;
}

.footer {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .topBtn {
    .flexRCC();
    min-width: 168rpx;
    height: 72rpx;
    padding: 0 24rpx;
    border-radius: 12rpx;
    border: 2rpx solid @primary-color;

    color: @primary-color;
    font-size: 30rpx;
    text-align: center;
    line-height: 76rpx;
    font-weight: 500;
  }
}
