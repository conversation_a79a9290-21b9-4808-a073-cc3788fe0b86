.bgColor {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;

  width: 100%;
  height: 940rpx;
  background: linear-gradient(#0092ff 0%, #f5f6fa 100%);
}

.header {
  padding-top: 0 !important;
  background: linear-gradient(#0092ff 0%, #f5f6fa 100%);
  background-size: 100% 940rpx;
  background-color: transparent !important;
  color: #fff !important;
}

.no-job {
  margin: 16rpx 24rpx 0 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background: #ffffff;
}

.no-job-title {
  color: #000000d9;
  font-weight: bold;
  font-size: 34rpx;
}

.no-job-img {
  display: block;
  width: 96rpx;
  height: 96rpx;
  margin: 32rpx auto 0 auto;
}

.no-job-text {
  color: #000000a6;
  font-size: 30rpx;
  margin-top: 24rpx;
  text-align: center;
}

.no-job-btn-box {
  display: flex;
  justify-content: center;
}

.no-job-btn {
  width: 212rpx !important;
  height: 80rpx !important;
  margin: 24rpx auto 0 auto !important;
  display: block !important;
  border-radius: 12rpx !important;
}

.no-job-btn-text {
  font-size: 30rpx;
  font-weight: bold;
}

.auditTip {
  line-height: 42rpx;
  padding: 16rpx 0;

  color: #fff;
  font-size: 30rpx;
  font-weight: 700;
  text-align: center;
}

/* 安全号*/
.safeNumRow {
  width: 702rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  margin: 0 auto;
  border: 2rpx solid var(--25, rgba(255, 255, 255, 0.25));
  background: rgba(255, 255, 255, 0.1);

  color: #fff;
  font-size: 30rpx;

  .safeNumTitle {
    height: 42rpx;
    line-height: 42rpx;
  }

  .safeNumBox {
    .flexRC();
    flex-wrap: wrap;
    margin: 0 -4rpx;
  }

  .safeNumItem {
    .flexRCC();
    min-width: 82rpx;
    max-width: fit-content;
    height: 56rpx;
    padding: 8rpx 0;
    background-color: #90cefe;
    border-radius: 8rpx;
    margin: 16rpx 4rpx 0;
  }
}

/* 置顶优势*/
.topRow {
  width: 702rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  background-color: #fff;
  margin: 16rpx auto 0;

  .topTipBox {
    position: relative;
    padding: 16rpx 24rpx;
    border-radius: 16rpx;
    background: linear-gradient(88deg, #4fc1ff, #0092ff);
    box-shadow: -2rpx -2rpx 0 0 rgba(0, 85, 145, 0.2) inset, 2rpx 2rpx 0 0 rgba(255, 255, 255, 0.25) inset;

    color: #fff;
  }

  .tip {
    font-size: 30rpx;
    font-weight: 700;
  }

  .topDetailBox {
    .flexRC();
    width: 145rpx;
    height: 36rpx;
    margin-top: 14rpx;
    font-size: 26rpx;
  }

  .topDetailText {
    height: 36rpx;
    line-height: 36rpx;
    font-weight: 700;
  }

  .topRecruidImg {
    width: 128rpx;
    height: 128rpx;

    position: absolute;
    right: 8rpx;
    bottom: 0;
  }
}

/* 置顶选择城市*/
.topSelectCityBox {
  width: 702rpx;
  padding: 32rpx 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin: 16rpx auto 0;

  .titleRow {
    .flexRC();
    justify-content: space-between;
    font-size: 34rpx;

    .topCityTitle {
      .flexRCC();
      height: 48rpx;
      font-weight: 700;
    }

    .selectOther {
      .flexRCC();
      color: @primary-color;
      font-size: 30rpx;
    }

    .selectOtherJobText {
      height: 48rpx;
      .flexRCC();
    }

    .selectOtherJobIcon {
      .flexRCC();
      width: 40rpx;
      height: 48rpx;
    }
  }

  .selectCitys {
    .flexRC();
    flex-wrap: wrap;
    margin: 16rpx 0 0 -16rpx;

    .cityItem {
      .flexRCC();
      min-width: 148rpx;
      height: 72rpx;
      padding: 0 24rpx;
      background-color: #e0f3ff;
      border-radius: 8rpx;
      margin: 16rpx 0 0 16rpx;
      color: @primary-color;

      .selectTopCity {
        .flexRCC();
        height: 72rpx;
        padding-right: 16rpx;
      }

      .selectTopCityIcon {
        .flexRCC();
        height: 72rpx;
      }
    }

    .addMoreBtn {
      .flexRCC();
      min-width: 208rpx;
      height: 72rpx;
      padding: 0 24rpx;
      background-color: @primary-color;
      border-radius: 8rpx;
      margin: 16rpx 0 0 16rpx;

      color: white;
      font-size: 28rpx;

      .addMoreBtnText {
        .flexRCC();
        height: 72rpx;
        margin-left: 8rpx;
      }

      .addMoreBtnIcon {
        .flexRCC();
        height: 72rpx;
      }
    }
  }

  .topCitys {
    .flexRC();
    flex-wrap: wrap;
    color: @text85;
    font-size: 34rpx;
    margin-top: 32rpx;
  }
}

.updateformItemRow {
  height: 112rpx;
  margin: 16rpx 24rpx;
  padding: 32rpx 24rpx;
  background-color: #fff;
  color: @text85;
  font-size: 34rpx;
  border-radius: 16rpx;
}

/* 修改置顶：选择置顶时间*/

/* 我要置顶、预约置顶选择置顶时间*/
.selectTimePanel {
  width: 702rpx;
  padding: 32rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  margin: 16rpx auto 0;

  .selectTimeHeader {
    .flexRCSB();
    margin-bottom: 24rpx;

    .topTimeTitle {
      font-size: 34rpx;
      font-weight: 700;
    }

    .topTimeExpire {
      .flexRCC();
      height: 48rpx;
      padding: 0 16rpx;
      border-radius: 24rpx;
      background-color: #e0f3ff;
    }

    .topTimeExpireDesc {
      color: @primary-color;
      font-size: 26rpx;
      height: 48rpx;
      line-height: 49rpx;
    }
  }

  .selectTimeMain {
    .flex(column);
    gap: 16rpx;

    .selectTimeMeal {
      .flexRCSB();
      width: 100%;
      padding: 0 32rpx;
      position: relative;
      height: 122rpx;
      border-radius: 16rpx;
      border: 2rpx solid @primary-color;
      background-color: #e0f3ff;
      color: @primary-color;
    }

    .commonTimeMeal {
      .flexRCSB();
      width: 100%;
      padding: 0 32rpx;
      position: relative;
      height: 122rpx;
      border-radius: 16rpx;
      border: 2rpx solid #dfe1ea;
      background-color: #fafafb;
    }

    .selectTopIntegral {
      .flexRCC();
    }

    .selectTopTime {
      font-size: 38rpx;
      font-weight: 700;
      height: 122rpx;
      line-height: 122rpx;
    }

    .originIntegral {
      margin-right: 8rpx;
      font-size: 26rpx;
      font-weight: 400;
      height: 30rpx;
      line-height: 36rpx;
      vertical-align: bottom;
      color: @text65;
      text-decoration: line-through;
    }

    .discountIntegral {
      font-size: 42rpx;
      font-weight: 700;
      height: 122rpx;
      line-height: 122rpx;
    }

    .discountUnit {
      font-size: 26rpx;
      font-weight: 400;
      height: 30rpx;
      line-height: 36rpx;
      vertical-align: bottom;
    }

    .discountTip {
      .flexRCC();
      position: absolute;
      top: -12rpx;
      right: -2rpx;
      height: 42rpx;
      padding: 0 16rpx;
      border-radius: 16rpx 16rpx 0 16rpx;
      background-color: #e8362e;
    }

    .discountTipText {
      display: flex;
      align-items: center;
      color: #ffefde;
      font-size: 24rpx;
      font-weight: 700;
    }
  }
}

/* 置顶规则*/
.topRuleTitle {
  width: 702rpx;
  padding: 24rpx 16rpx 16rpx;
  margin: 16rpx auto 0;
  color: @text85;
  font-size: 30rpx;
}

.topRuleText {
  width: 702rpx;
  padding: 0 16rpx;
  margin: 0 auto 0;

  color: @text65;
  font-size: 26rpx;
  line-height: 48rpx;
}

/* 底部确定置顶*/

.recharge-tip {
  width: 750rpx;
  height: 88rpx;
  line-height: 88rpx;
  background: #f5f6fa;

  color: @text85;
  font-size: 34rpx;
  text-align: center;
}

.number {
  color: #09f;
  font-weight: 500;
  margin: 0 4rpx;
}
