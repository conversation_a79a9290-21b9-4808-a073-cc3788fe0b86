.header-cont {
  position: sticky;
  top: 0;
  z-index: 9;
}

.city-box {
  padding-bottom: calc(32rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}

.city-list {
  padding: 16rpx 32rpx;

  .list-title {
    line-height: 44rpx;
    padding: 8rpx 0 24rpx;
    color: rgba(0, 0, 0, 0.65);
    font-size: 28rpx;
  }

  .list-card {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 24rpx;
  }

  .list-card-item {
    height: 72rpx;
    padding: 0 8rpx;
    background: #fff;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(0, 0, 0, 0.85);
    font-size: 28rpx;
    text-align: center;
    position: relative;
    overflow: hidden;
    .active('');

    &.item-visible {
      overflow: visible;
    }

    /* 已选中的城市*/
    &.checked {
      color: white;
      background-color: @primary-color;
    }

    .item-img {
      position: absolute;
      top: -10rpx;
      right: -10rpx;
      width: 36rpx;
      height: 36rpx;
    }

    .text {
      width: 100%;
      .ellip;
    }
  }
}
