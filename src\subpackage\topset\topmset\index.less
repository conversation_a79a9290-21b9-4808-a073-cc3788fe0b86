.header {
  position: relative;
  background-color: #09F;
}

.header-bg {
  position: absolute;
  right: 0;
  bottom: -20rpx;

  width: 356rpx;
  height: 576rpx;
}

.header-title {
  position: relative;
  display: flex;
  align-items: center;
}

.title-text {
  width: 534rpx;
  padding: 40rpx 42rpx 30rpx 30rpx;
}

.title-text-img {
  display: block;
  width: 462rpx;
  height: 48rpx;
}

.text {
  padding: 18rpx 0 0 2rpx;
  color: #FFF;
  font-size: 28rpx;
  line-height: 40rpx;
}

.title-img {
  width: 200rpx;
  height: 130rpx;
}

.header-imgs {
  position: relative;
  width: 100%;
  min-height: 196rpx;
  padding: 0 32rpx 16rpx;
  font-size: 0;
}

.header-imgs-grid {
  position: relative;
  background: #FFF;
  border-radius: 8rpx;
  overflow: hidden;
}

.imgs-grid::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);

  width: 686rpx;
  height: 2rpx;
  background: #F5F6FA;
}

.imgs-item {
  border-right: 2rpx solid #F5F6FA;
}

.item {
  width: 218rpx;
  height: 172rpx;
  display: block;
}

.recharge-tip {
  width: 750rpx;
  height: 84rpx;
  background: #f5f6fa;
  text-align: center;
  line-height: 84rpx;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.85);
}

.top-rule {
  background: #FFF;
  padding: 36rpx 32rpx 0;
  margin: 16rpx 0 18rpx;
  border-radius: 8rpx 8rpx 0 0;
}

.top-rule-title {
  color: rgba(0, 0, 0, 0.65);
  font-size: 28rpx;
  line-height: 44rpx;
  margin-bottom: 16rpx;
}

.top-rule-item {
  color: rgba(0, 0, 0, 0.45);
  font-size: 24rpx;
  line-height: 40rpx;
  padding-bottom: 36rpx;
}
