.container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: #fff;

  .title {
    margin-top: 50rpx;
    margin-left: 64rpx;
    color: #333;
    font-size: 40rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    height: 56rpx;

    .image {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }
  }

  .tabs {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 710rpx;
    height: 80rpx;
    margin: 52rpx 64rpx 0;

    .tab {
      position: relative;
      margin-right: 96rpx;
      color: rgba(0, 0, 0, 0.85);
      font-size: 32rpx;
      line-height: 48rpx;
      height: 48rpx;
      font-weight: bold;
    }

    .active {
      position: relative;
      color: #0092ff;
      font-weight: bold;

      &::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: -16rpx;
        transform: translateX(-50%);
        background: #0092ff;
        width: 48rpx;
        height: 6rpx;
        border-radius: 3rpx;
      }
    }
  }

  .body {
    position: relative;
    width: 100%;
    padding: 0 64rpx;

    .item {
      width: 622rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fff;
      border-bottom: 2rpx solid #eff1f6;
      margin-top: 48rpx;
      position: relative;

      .phone-icon {
        color: #0092FF;
        margin-right: 16rpx;
      }

      .default-icon {
        color: #000;
        margin-right: 16rpx;
      }

      .item-input {
        flex: 1;
        height: 88rpx;
        line-height: 88rpx;
        font-size: 28rpx;
        caret-color: #0092ff;
      }

      .get-code-btn {
        color: #0097fe;
        font-size: 28rpx;
      }
    }

    .first {
      margin-top: 48rpx;
    }
  }

  .word-tips {
    width: 100%;
    color: #f74742;
    font-size: 24rpx;
    min-height: 40rpx;
    line-height: 40rpx;
    text-align: center;
    margin-top: 16rpx;
  }

  .login-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 14rpx;
    margin-top: 26rpx;
    background: #0092ff;
    margin-right: 0;
    border: 2rpx solid #0092ff;
    color: white;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}

.agreement {
  padding: 48rpx 0;
  color: @text-third-color;
  font-size: 24rpx;
  line-height: 40rpx;

  .box {
    display: flex;

    .icon {
      display: flex;
      align-items: flex-start;
      color: #d5d9e5;
      font-size: 48rpx;
    }

    .color {
      color: @primary-color;
    }
  }
}