.video-shoot {
  min-height: 100vh;
  background-color: #000;
}

.mask {
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 3;
  width: 750rpx;
  max-height: 740rpx;
  background-color: rgba(0, 0, 0, 0.6);
}

.qa-list {
  flex: 1 auto;
  overflow-y: auto;
  position: relative;
  box-sizing: content-box;
}

.qa-item {
  height: 100%;
  position: relative;
  padding: 0 32rpx;
  margin-bottom: 24rpx;
}

.qa-item-content {
  position: absolute;
  bottom: 12rpx;
}

.qa-title {
  font-size: 32rpx;
  color: #0092FF;
  line-height: 48rpx;
}

.qa-content {
  font-size: 32rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  line-height: 48rpx;
  padding-top: 16rpx;
}

.resume-mask {
  max-height: none;

  .qa-list {
    max-height: 416rpx;
    box-sizing: content-box;
  }
}

.camera {
  position: fixed;
  left: 0;
  top: 0;
  width: 750rpx;
  height: 100vh;
  z-index: 2;
}

.person-outline {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2;
  width: 750rpx;
  height: 100vh;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 3;
  width: 750rpx;
  /* height: 208rpx;*/
  padding-top: 16rpx;
  padding-bottom: calc(32rpx + constant(safe-area-inset-bottom));/* 兼容 IOS<11.2 */
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom)); /* 兼容 IOS>11.2 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
}

.shoot-btn {
  width: 160rpx;
  height: 160rpx;
  margin: 0 60rpx;
}

.shoot-btn-active {
  text-align: center;
  color: #fff;
}

.shoot-text {
  margin-top: 20rpx;
  font-weight: bold;
}

.sub-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
}

.sub-btn-text {
  margin-left: 8rpx;
  line-height: 44rpx;
}

.sub-btn-wrap {
  display: flex;
  justify-content: center;
  align-items: center;

  .sub-btn-style-2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 28rpx;
    line-height: 44rpx;
  }

  .album {
    margin-left: 72rpx;
  }
}

.shoot-progress {
  width: 160rpx;
  height: 160rpx;
  margin: 0 60rpx;
}

.stop-btn-wrap {
  position: absolute;
  width: 160rpx;
  height: 160rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  left: 296rpx;
  top: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-round {
  width: 84rpx;
  height: 84rpx;
  border-radius: 50%;
  background-color: #0092FF;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-square {
  width: 30rpx;
  height: 30rpx;
  background: #fff;
  border-radius: 8rpx;
}

.qa-swiper {
  height: 210rpx;
  position: relative;
  pointer-events: none;
}
