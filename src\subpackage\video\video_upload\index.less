.video-upload {
  background: #000;
  width: 100%;
  height: 100vh;
}

.resume-video {
  position: relative;
  overflow: hidden;
}

.notice {
  position: absolute;
  width: 686rpx;
  z-index: 2;
  margin: 16rpx 32rpx;
  padding: 18rpx 22rpx 18rpx 34rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

.notice-img {
  width: 106rpx;
  height: 128rpx;
  margin-right: 38rpx;
  flex-shrink: 0;
}

.notice-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  line-height: 44rpx;
  white-space: pre-line;
}

.play-btn {
  width: 108rpx;
  height: 108rpx;
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 2;
  transform: translate(-50%, -50%);
}

.video {
  width: 750px;
  position: absolute;
  z-index: 1;
}

.video-upload-foot {
  position: absolute;
  bottom: 0;

  .progress-wrap {
    position: relative;

    .progress-slider {
      position: absolute;
      top: -14rpx;
      z-index: 9000;
      width: 750rpx;
      margin: 0;
      opacity: 0;
    }

    .progress-bar {
      width: 100%;
      background-color: #4d4d4d;
      height: 8rpx;
    }

    .progress-item {
      width: 0;
      height: 8rpx;
      background: #fff;
      border-radius: 0 6rpx 6rpx 0;
      transition: width 0.25s linear;
    }
  }

  .video-upload-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 750rpx;
    height: 120rpx;
    padding: 28rpx 32rpx;
  }

  .safe-area {
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom);
    background-color: transparent;
  }
}

.video-timeline {
  border-radius: 16rpx;
  font-size: 28rpx;
  padding: 14rpx 18rpx;
  opacity: 0.7;
  line-height: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn {
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-item {
  line-height: 64rpx;
  text-align: center;
  height: 64rpx;
  padding: 0 24rpx;
  background: #0092FF;
  border-radius: 8px;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
}

.ghost {
  background: #E5F4FF;
  color: #0092FF;
  margin-right: 16rpx;
}

.upload-progress {
  width: 164rpx;
  height: 164rpx;
}

.watermark-canvas {
  position: absolute;
  opacity: 0;
  top: 0;
  right: 100vw;
  z-index: -1;
}

.project-upload-mask {
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  position: fixed;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;

  .project-upload-progress {
    width: 448rpx;
    height: 168rpx;
    padding: 56rpx 0 28rpx;
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 22rpx;

    .upload-gif {
      width: 220rpx;
      height: 20rpx;
    }

    .upload-text {
      height: 44rpx;
      line-height: 44rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #fff;
      padding-top: 20rpx;
    }
  }
}
