import { store, actions, dispatch } from '@/store/index'

// 归因认证落地页
export const ascribeFirmAuth = {
  url: '/company-auth-attribution', // 落地页地址
  sharePage: 'Company_invitation_share_page', // 分享page
  sharePath: 'Company_invitation_share_path', // 分享path
}

// 企业认证相关页面
// 需要剔除web的实名认证页面'subpackage/member/webFirmAuth/index',
const firmAuthRelativePaths = [
  'subpackage/userauth/auth/index', // 登录
  'subpackage/member/firmAuth/index', // 企业认证
  'subpackage/member/firmAuthorization/index', // 招聘授权书
  'subpackage/member/firmCertificate/index', // 在职证明
  'subpackage/member/firmCompanyLicense/index', // 营业执照
  'subpackage/member/firmIdentityAuth/index', // 法人签名+法人身份证
  'subpackage/member/firmLegalAuth/index', // 企业法人
  'subpackage/member/firmLicense/index', // 营业执照
  'subpackage/member/firmProxyAuth/index', // 公司授权书认证
  'subpackage/member/firmVariousVerify/index', // 直接跳转到子账号申请的-审核结果页
  'subpackage/member/realname/index', // 实名认证
  'subpackage/member/camera/index',
  'subpackage/common/watermark_camera/index',
  'subpackage/mp-ecard-sdk/index/index', // 人脸识别
  'subpackage/web-view/index', // 服务协议
  'subpackage/certification/replenish/index', // 补充验证
  'subpackage/certification/submit/index',
  'subpackage/member/change-account/index',
  'subpackage/mp-ecard-sdk/protocol/service/index',
  'subpackage/mp-ecard-sdk/protocol/eid/index',
  'subpackage/member/unbindRealname/mandatory_certification/index', // 强制实名
  'subpackage/member/unbindRealname/unbind_one/index',
  'subpackage/member/unbindRealname/unbind_two/index',
  'subpackage/member/unbindRealname/unbind_three/index',
]

/** 判断是当前归因页面 */
export const isAscribeAuthLandPage = (
  url: string, // 页面地址
) => {
  // 存在url编码的情况需要解码对比
  return [decodeURIComponent(url), url].includes(ascribeFirmAuth.url)
}

/** 归因企业认证流程中断或完成流程后清除归因缓存数据
 * 后端依赖track_seed归因，header透传trackSeed，进入落地页后续认证都要认证归因
 * 从归因落地页开始的企业认证流程成功或已经认证需要打开职位发布页回显关联jobid职位信息，
 * 跳出归因落地页发起的企业认证流程不在回显管理职位信息
*/
export const interruptAscribeFirmAuth = async (
  path: string, // 页面地址
) => {
  const { firmAuthAscribeToData } = store.getState().user
  const { isAscribeToFirmAuth, jobId } = firmAuthAscribeToData
  if (isAscribeToFirmAuth && !firmAuthRelativePaths.includes(path) && jobId) {
    dispatch(actions.userActions.setFirmAuthAscribeToData({ ...firmAuthAscribeToData, jobId: '' }))
  }
}

/** 用户扫码或者点击分享卡片进入认证归因落地页缓存归因数据 */
export const cacheAscribeFirmAuthData = async (options) => {
  const { bizType, url, track_seed, refid, jobId } = options || {}
  const result = {
    isAscribeToFirmAuth: true, // 是否是企业认证归因
    bizType,
    jobId, // 招工id
    refid,
    track_seed,
    url,
  }
  dispatch(actions.userActions.setFirmAuthAscribeToData(result))
  // // 清楚web端企业认证流程缓存标识， web端企业认证需要pctoken登录
  // // 如果用户扫描pc企业认证二维码进入页面直接退出， 用户点击分享卡片或者直接扫码落地页登录会印象归因认证流程
  // dispatch(actions.userActions.setWebTokenData({
  //   isWebFirmAuth: false,
  //   token: '',
  //   authId: '',
  //   webFromData: {},
  //   shortToken: '',
  // }))
  // const { userId } = store.getState().storage.userState
  // wx.$.collectEvent.config({ user_unique_id: userId || '' })
}

/** 认证归因落地页分享透传归因参数
 * 需要用归因数据替换分享接口动态生成的部分
 */
export const transAscribeParameters = (path, options) => {
  let result = path
  try {
    const formatUrl = decodeURIComponent(path)
    const [page] = formatUrl.split('?')
    // 接口返回的query参数更换为当前归因进入页面参数，参数透传
    const queryMap = wx.$.u.getUrlAllParams(formatUrl)
    const query = { ...queryMap, ...options }
    const queryStr = Object.keys(query)
      .reduce((list, ky) => {
        if (ky != 'isLogin') {
          list.push(`${ky}=${query[ky]}`)
        }
        return list
      }, [])
      .join('&')
    result = `${page}${queryStr ? '?' : ''}${queryStr}`
  } catch {
    // console.log('分享透传归因参数报错')
  }
  return result
}

/** 获取归因认证jobid */
export const getAscribeToAuthJobId = () => {
  const { isAscribeToFirmAuth, jobId } = store.getState().user.firmAuthAscribeToData
  // 在落地页点击立即入驻的企业认证流程中 && jobid存在直接跳转发布职位
  return isAscribeToFirmAuth && jobId ? jobId : ''
}

/** 归因认证落地页点击立即入注，跳转登录跳过新牛人完善简历流程，强制认证
 * 跳出落地页立即入驻主流程jobid会被清空
*/
export const forceAscribeToFirmAuth = () => {
  const { firmAuthAscribeToData } = store.getState().user
  const { isAscribeToFirmAuth, jobId } = firmAuthAscribeToData
  return isAscribeToFirmAuth && !!jobId
}
