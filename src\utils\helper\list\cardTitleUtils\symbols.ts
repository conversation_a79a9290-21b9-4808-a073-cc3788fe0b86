/**
 * 估算文本宽度，支持中文和英文全角字符
 *
 * @param {string} text - 需要估算宽度的文本
 * @param {number} charWidth - 单个字符的基准宽度（px）
 * @returns {number} 文本的宽度（px）
 */
export function estimateTextWidth(text: string, charWidth: number): number {
  let width = 0
  // eslint-disable-next-line no-restricted-syntax
  for (const ch of text) {
    if (Object.prototype.hasOwnProperty.call(symbols, ch)) {
      width += charWidth * symbols[ch]
    } else if (/[\u4e00-\u9fa5]/.test(ch) || /[\uFF21-\uFF3A\uFF41-\uFF5A]/.test(ch)) {
      // 中文或英文全角
      width += charWidth * 1.0
    } else {
      width += charWidth * 1.5 // 兜底 尽量放大 1.5个字符的宽度
    }
  }
  return width
}

// 字符号宽度比例表 --排除中文中文都是1
const symbols = {
  // 数字
  0: 0.6000689338235294,
  1: 0.41004136029411764,
  2: 0.6000689338235294,
  3: 0.6000689338235294,
  4: 0.6000689338235294,
  5: 0.6000689338235294,
  6: 0.6000689338235294,
  7: 0.5530790441176471,
  8: 0.6000689338235294,
  9: 0.6000689338235294,
  // 英文符号
  '!': 0.3330652573529412,
  '@': 0.8660386029411765,
  '#': 0.6000689338235294,
  $: 0.6000689338235294,
  '%': 0.99609375,
  '^': 0.5450367647058824,
  '&': 0.7510340073529411,
  '*': 0.5160845588235294,
  '(': 0.3330652573529412,
  ')': 0.3330652573529412,
  '-': 0.6050091911764706,
  _: 0.5,
  '=': 0.6050091911764706,
  '+': 0.6050091911764706,
  '[': 0.3330652573529412,
  ']': 0.3330652573529412,
  '{': 0.3330652573529412,
  '}': 0.3330652573529412,
  ';': 0.2750459558823529,
  ':': 0.2750459558823529,
  "'": 0.2810202205882353,
  '"': 0.4880514705882353,
  ',': 0.2750459558823529,
  '.': 0.2750459558823529,
  '<': 0.6050091911764706,
  '>': 0.6050091911764706,
  '/': 0.5,
  '?': 0.5580193014705882,
  '\\': 0.5,
  '|': 0.21403952205882354,
  '`': 0.3330652573529412,
  '~': 0.5,
  // 特殊符号
  '±': 0.6050091911764706,
  '×': 0.6050091911764706,
  '÷': 0.6050091911764706,
  '≈': 0.7840073529411765,
  '≠': 0.7840073529411765,
  '≤': 0.7840073529411765,
  '≥': 0.7840073529411765,
  '∞': 0.9910386029411765,
  '∑': 0.6140854779411765,
  '∏': 0.7270220588235294,
  '∫': 0.5380284926470589,
  '√': 1,
  '∇': 0.6119025735294118,
  '∂': 0.6281020220588235,
  '∆': 0.6991038602941176,
  '∝': 0.9610523897058824,
  '¢': 0.6000689338235294,
  '£': 0.6000689338235294,
  '€': 0.7180606617647058,
  '¥': 0.6000689338235294,
  '₩': 1.0244715073529411,
  '₽': 0.4950597426470588,
  '₹': 0.6000689338235294,
  '°': 0.33605238970588236,
  '′': 0.2890625,
  '″': 0.4970128676470588,
  '℃': 0.9260110294117647,
  '℉': 0.8760340073529411,
  '©': 0.8020450367647058,
  '®': 0.8020450367647058,
  '™': 0.9030330882352942,
  '℗': 0.8020450367647058,
  '←': 1,
  '↑': 1,
  '→': 1,
  '↓': 1,
  '↔': 1,
  '↕': 0.5,
  '⇒': 1,
  '⇔': 1,
  '§': 0.6000689338235294,
  '¶': 0.5860523897058824,
  '†': 0.6100643382352942,
  '‡': 0.6100643382352942,
  '※': 1,
  '•': 0.5,
  '…': 1,
  '‥': 1,
  '‧': 0.5,
  // 中文
  '，': 1,
  '。': 1,
  '！': 1,
  '？': 1,
  '；': 1,
  '：': 1,
  '“': 0.6140854779411765,
  '”': 0.6140854779411765,
  '‘': 0.3720128676470588,
  '’': 0.3720128676470588,
  '（': 1,
  '）': 1,
  '【': 1,
  '】': 1,
  '《': 1,
  '》': 1,
  '、': 1,
  '——': 2,
  '……': 2,
  '￥': 1,
  '·': 0.5,
  '『': 1,
  '』': 1,
  '「': 1,
  '」': 1,
  '～': 1,
  '﹏': 1,
  '．': 1,
  '／': 1,
  '＂': 1,
  '＇': 1,
  '｜': 1,
  // 全角符号
  '＠': 1,
  '＃': 1,
  '＄': 1,
  '％': 1,
  '＾': 1,
  '＆': 1,
  '＊': 1,
  '－': 1,
  '＿': 1,
  '＝': 1,
  '＋': 1,
  '［': 1,
  '］': 1,
  '｛': 1,
  '｝': 1,
  '＜': 1,
  '＞': 1,
  '＼': 1,
  '｀': 1,
  '　': 1,
  // 英文字母
  A: 0.6791130514705882,
  B: 0.6900275735294118,
  C: 0.7320772058823529,
  D: 0.7200137867647058,
  E: 0.6420036764705882,
  F: 0.58203125,
  G: 0.7560891544117647,
  H: 0.7360983455882353,
  I: 0.25700827205882354,
  J: 0.5380284926470589,
  K: 0.7140395220588235,
  L: 0.5920266544117647,
  M: 0.9030330882352942,
  N: 0.7320772058823529,
  O: 0.7750459558823529,
  P: 0.6551011029411765,
  Q: 0.7750459558823529,
  R: 0.6970358455882353,
  S: 0.6510799632352942,
  T: 0.6150045955882353,
  U: 0.7340303308823529,
  V: 0.6590073529411765,
  W: 0.9530101102941176,
  X: 0.6640625,
  Y: 0.6910615808823529,
  Z: 0.6390165441176471,
  // 小写英文字母
  a: 0.5720358455882353,
  b: 0.6020220588235294,
  c: 0.5610064338235294,
  d: 0.6020220588235294,
  e: 0.5670955882352942,
  f: 0.34650735294117646,
  g: 0.6020220588235294,
  h: 0.5770909926470589,
  i: 0.2750459558823529,
  j: 0.2760799632352941,
  k: 0.5530790441176471,
  l: 0.25402113970588236,
  m: 0.8840762867647058,
  n: 0.5811121323529411,
  o: 0.6000689338235294,
  p: 0.6020220588235294,
  q: 0.6020220588235294,
  r: 0.3740808823529412,
  s: 0.5250459558823529,
  t: 0.3620174632352941,
  u: 0.58203125,
  v: 0.5060891544117647,
  w: 0.7771139705882353,
  x: 0.537109375,
  y: 0.5260799632352942,
  z: 0.5060891544117647,
  // 其它符号
  '♠': 0.53125,
  '♣': 0.65625,
  '♥': 0.59375,
  '♦': 0.5103400735294118,
  '★': 1,
  '☆': 1,
  '◆': 1,
  '◇': 1,
  '●': 1,
  '○': 1,
  '■': 1,
  '□': 1,
  '▲': 1,
  '△': 1,
  '▼': 1,
  '▽': 1,
  '▶': 0.8799402573529411,
  '◀': 1,
  '☺': 0.7369025735294118,
  '☻': 0.7369025735294118,
  '☹': 0.7369025735294118,
  '☀': 1,
  '☁': 1,
  '☂': 1,
  '☃': 1,
  '♨': 1,
  '☎': 1,
  '☏': 0.8650045955882353,
  '✿': 0.826171875,
  '❀': 0.8150275735294118,
  '❁': 0.7890625,
  '❂': 0.7890625,
  '❃': 0.70703125,
  '❄': 0.6870404411764706,
  '❅': 0.6958869485294118,
  '❆': 0.6889935661764706,
  '❈': 0.787109375,
  '❉': 0.712890625,
  '❊': 0.791015625,
  '❋': 0.78515625,
  '❖': 0.7842371323529411,
  '❘': 0.30135569852941174,
  '❙': 0.27688419117647056,
  '❚': 0.4150965073529412,
  '❛': 0.39211856617647056,
  '❜': 0.39211856617647056,
  '❝': 0.66796875,
  '❞': 0.66796875,
  '❡': 0.7319623161764706,
  '❢': 0.5440027573529411,
  '❣': 0.5440027573529411,
  '❤': 0.91015625,
  '❥': 0.6670496323529411,
  '❦': 0.759765625,
  '❧': 0.759765625,
  '₦': 0.80859375,
  '₨': 0.62890625,
  '₪': 0.9146369485294118,
  '₫': 0.62109375,
  '₭': 0.7822840073529411,
  '₮': 0.6108685661764706,
  '₯': 1.125,
  '₰': 0.6709558823529411,
  '₱': 0.779296875,
  '₲': 0.7779181985294118,
  '₳': 0.7221966911764706,
  '₴': 0.5850183823529411,
  '₵': 0.7221966911764706,
  '₶': 0.6768152573529411,
  '₷': 0.9389935661764706,
  '₸': 0.5561810661764706,
  '₺': 0.5561810661764706,
  '₻': 0.9839154411764706,
  '₼': 0.5708869485294118,
  '₾': 0.7529871323529411,
  '₿': 0.6377527573529411,
  '∅': 0.7173713235294118,
  '∈': 0.7840073529411765,
  '∉': 0.7840073529411765,
  '∋': 0.576171875,
  '∌': 0.5889246323529411,
  '∍': 0.5093060661764706,
  '∎': 0.5220588235294118,
  '∐': 0.8232996323529411,
  '−': 0.8241038602941176,
  '∓': 0.7954963235294118,
  '∔': 0.5986902573529411,
  '∕': 0.8220358455882353,
  '∖': 0.8037683823529411,
  '∗': 0.5234375,
  '∘': 0.35500919117647056,
  '∙': 0.42003676470588236,
  '∛': 0.7486213235294118,
  '∜': 0.7241498161764706,
  '∟': 0.8041130514705882,
  '∠': 0.7860753676470589,
  '∡': 0.6006433823529411,
  '∢': 0.60546875,
  '∤': 0.3330652573529412,
  '∥': 0.6540670955882353,
  '∦': 0.6540670955882353,
  '∧': 0.7540211397058824,
  '∨': 0.7540211397058824,
  '∩': 0.7200137867647058,
  '∪': 0.7200137867647058,
  '∴': 0.9080882352941176,
  '∵': 0.9080882352941176,
  '∶': 0.5,
  '∷': 0.8150275735294118,
  '∸': 0.5625,
  '∹': 0.5757123161764706,
  '∺': 0.5796185661764706,
  '∻': 0.5596277573529411,
  '∼': 0.7840073529411765,
  '∽': 0.7840073529411765,
  '∾': 0.54296875,
  '∿': 0.5835248161764706,
  '≀': 0.39016544117647056,
  '≁': 0.7840073529411765,
  '≂': 0.5796185661764706,
  '≃': 0.5845588235294118,
  '≄': 0.6709558823529411,
  '≅': 0.6963465073529411,
  '≆': 0.6294806985294118,
  '≇': 0.611328125,
  '≉': 0.7840073529411765,
  '≊': 0.603515625,
  '≋': 0.6709558823529411,
  '≌': 0.7840073529411765,
  '≍': 0.6030560661764706,
  '≎': 0.5971966911764706,
  '≏': 0.5928308823529411,
  '≐': 0.5600873161764706,
  '≑': 0.6382123161764706,
  '≒': 0.7840073529411765,
  '≓': 0.6323529411764706,
  '≔': 0.763671875,
  '≕': 0.763671875,
  '≖': 0.5786994485294118,
  '≗': 0.595703125,
  '≘': 0.5889246323529411,
  '≙': 0.6079963235294118,
  '≚': 0.619140625,
  '≛': 0.6460248161764706,
  '≜': 0.6362591911764706,
  '≝': 0.5630744485294118,
  '≞': 0.5747931985294118,
  '≟': 0.58984375,
  '≡': 0.7840073529411765,
  '≢': 0.7840073529411765,
  '≣': 0.5928308823529411,
  '≦': 0.7840073529411765,
  '≧': 0.7840073529411765,
  '≨': 0.6221277573529411,
  '≩': 0.5893841911764706,
  '≪': 0.7334558823529411,
  '≫': 0.701171875,
  '≬': 0.35799632352941174,
  '≭': 0.5533088235294118,
  '≮': 0.7840073529411765,
  '≯': 0.7840073529411765,
  '≰': 0.7840073529411765,
  '≱': 0.7840073529411765,
  '≲': 0.5639935661764706,
  '≳': 0.5674402573529411,
  '≴': 0.5830652573529411,
  '≵': 0.5806525735294118,
  '≶': 0.60546875,
  '≷': 0.60546875,
  '≸': 0.560546875,
  '≹': 0.5767463235294118,
  '≺': 0.5674402573529411,
  '≻': 0.578125,
  '≼': 0.5962775735294118,
  '≽': 0.578125,
  '≾': 0.5854779411764706,
  '≿': 0.58203125,
  α: 0.6290211397058824,
  β: 0.5960477941176471,
  γ: 0.5550321691176471,
  δ: 0.6000689338235294,
  ε: 0.5420496323529411,
  ζ: 0.4730009191176471,
  η: 0.5811121323529411,
  θ: 0.5630744485294118,
  ι: 0.29802389705882354,
  κ: 0.5630744485294118,
  λ: 0.5811121323529411,
  μ: 0.58203125,
  ν: 0.5330882352941176,
  ξ: 0.4870174632352941,
  ο: 0.6000689338235294,
  π: 0.6650965073529411,
  ρ: 0.6130514705882353,
  σ: 0.6130514705882353,
  τ: 0.5,
  υ: 0.5830652573529411,
  φ: 0.75,
  χ: 0.5400965073529411,
  ψ: 0.7110523897058824,
  ω: 0.7560891544117647,
  Α: 0.6791130514705882,
  Β: 0.6900275735294118,
  Γ: 0.5920266544117647,
  Δ: 0.6991038602941176,
  Ε: 0.6420036764705882,
  Ζ: 0.6390165441176471,
  Η: 0.7360983455882353,
  Θ: 0.7750459558823529,
  Ι: 0.25700827205882354,
  Κ: 0.7140395220588235,
  Λ: 0.6590073529411765,
  Μ: 0.9030330882352942,
  Ν: 0.7320772058823529,
  Ξ: 0.6351102941176471,
  Ο: 0.7750459558823529,
  Π: 0.7270220588235294,
  Ρ: 0.6551011029411765,
  Σ: 0.6140854779411765,
  Τ: 0.6150045955882353,
  Υ: 0.6910615808823529,
  Φ: 0.7920496323529411,
  Χ: 0.6640625,
  Ψ: 0.8110064338235294,
  Ω: 0.7470128676470589,
  é: 0.5670955882352942,
  è: 0.5670955882352942,
  ê: 0.5670955882352942,
  ë: 0.5670955882352942,
  á: 0.5720358455882353,
  à: 0.5720358455882353,
  â: 0.5720358455882353,
  ä: 0.5720358455882353,
  í: 0.2750459558823529,
  ì: 0.2750459558823529,
  î: 0.2750459558823529,
  ï: 0.2750459558823529,
  ó: 0.6000689338235294,
  ò: 0.6000689338235294,
  ô: 0.6000689338235294,
  ö: 0.6000689338235294,
  ú: 0.58203125,
  ù: 0.58203125,
  û: 0.58203125,
  ü: 0.58203125,
  ñ: 0.5811121323529411,
  ç: 0.5610064338235294,
  ø: 0.6000689338235294,
  å: 0.5720358455882353,
  œ: 0.9370404411764706,
  æ: 0.9070542279411765,
  ß: 0.6460248161764706,
  '\t': 0,
  '\n': 0,
  '\r': 0,
  '\u000b': 0,
  '\f': 0,
  ' ': 0.3330652573529412,
  ' ': 0.5,
  ' ': 1,
  ' ': 0.20002297794117646,
  '‹': 0.3851102941176471,
  '›': 0.3851102941176471,
  '«': 0.5650275735294118,
  '»': 0.5650275735294118,
  '‚': 0.3720128676470588,
  '„': 0.6140854779411765,
  '〔': 1,
  '〕': 1,
  '〈': 1,
  '〉': 1,
  '—': 1,
  '–': 0.8241038602941176,
  '―': 1,
  '‒': 0.3330652573529412,
  '﹋': 1,
  '﹌': 1,
  '﹒': 1,
  '﹔': 1,
  '﹕': 1,
  '﹖': 1,
  '﹗': 1,
  '﹑': 1,
  '↖': 1,
  '↗': 1,
  '↘': 1,
  '↙': 1,
  '⇐': 1,
  '⇑': 0.8650045955882353,
  '⇓': 0.8650045955882353,
  '⇕': 0.6021369485294118,
  '①': 1,
  '②': 1,
  '③': 1,
  '④': 1,
  '⑤': 1,
  '⑥': 1,
  '⑦': 1,
  '⑧': 1,
  '⑨': 1,
  '⑩': 1,
  '⑪': 1,
  '⑫': 1,
  '⑬': 1,
  '⑭': 1,
  '⑮': 1,
  '⑯': 1,
  '⑰': 1,
  '⑱': 1,
  '⑲': 1,
  '⑳': 1,
  Ⅰ: 1,
  Ⅱ: 1,
  Ⅲ: 1,
  Ⅳ: 1,
  Ⅴ: 1,
  Ⅵ: 1,
  Ⅶ: 1,
  Ⅷ: 1,
  Ⅸ: 1,
  Ⅹ: 1,
  Ⅺ: 1,
  Ⅻ: 1,
  ⅰ: 1,
  ⅱ: 1,
  ⅲ: 1,
  ⅳ: 1,
  ⅴ: 1,
  ⅵ: 1,
  ⅶ: 1,
  ⅷ: 1,
  ⅸ: 1,
  ⅹ: 1,
  '‰': 1.166015625,
  '‱': 1.4229090073529411,
  '㎏': 1,
  '㎜': 1,
  '㎝': 1,
  '㎞': 1,
  '㎡': 1,
  '㏄': 1,
  '㏎': 1,
  '㏑': 1,
  '㏒': 1,
  '㏕': 1,
  '㏖': 0.8650045955882353,
  '㏗': 1,
  '㏘': 1,
  '㏙': 1,
  '㏚': 1,
  '㏛': 0.8650045955882353,
  '㏜': 0.8650045955882353,
  '㏝': 0.8650045955882353,
  '㏞': 1,
  '㏟': 1,
  '♯': 0.5561810661764706,
  '♭': 1,
  '♪': 0.5464154411764706,
  '♫': 0.75,
  '♩': 1,
  '♬': 1,
  '☑': 0.830078125,
  '☒': 0.830078125,
  '☐': 0.830078125,
  '☝': 1,
  '☞': 1,
  '☟': 1,
  '☚': 0.6021369485294118,
  '☛': 0.6021369485294118,
  '☜': 1,
}
