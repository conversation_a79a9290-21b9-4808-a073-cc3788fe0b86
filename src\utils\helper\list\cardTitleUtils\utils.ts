import { getCharWidth, rpxToPx } from '@/utils/helper/common/index'
import { estimateTextWidth } from './symbols'
import { store } from '@/store/index'

/**
 * 比较两个距离的大小
 *
 * 该函数接受两个距离字符串作为输入，每个字符串可以表示公里（km）或米（m）
 * 它首先验证输入格式是否正确，然后将距离转换为相同的单位（公里），最后比较两个距离的大小
 *
 * @param distance1 第一个距离字符串，格式为数字后跟单位（例如，"5km" 或 "300m"）
 * @param distance2 第二个距离字符串，格式同上
 * @returns 如果第一个距离小于第二个距离，则返回 true；否则返回 false 如果输入格式无效或单位不支持，则返回false
 */
export function compareDistances(distance1, distance2) {
  // 正则表达式提取数字部分和单位
  const match1 = distance1.match(/^(\d+(\.\d+)?)(km|m)$/i)
  const match2 = distance2.match(/^(\d+(\.\d+)?)(km|m)$/i)

  if (!match1 || !match2) {
    return false // 处理无效输入格式
  }

  const num1 = parseFloat(match1[1])
  const unit1 = match1[3].toLowerCase()

  const num2 = parseFloat(match2[1])
  const unit2 = match2[3].toLowerCase()

  // 单位转换，都转换为千米
  let distance1Km
  if (unit1 === 'km') {
    distance1Km = num1
  } else if (unit1 === 'm') {
    distance1Km = num1 / 1000
  } else {
    return false // 处理无效单位
  }

  let distance2Km
  if (unit2 === 'km') {
    distance2Km = num2
  } else if (unit2 === 'm') {
    distance2Km = num2 / 1000
  } else {
    return false // 处理无效单位
  }

  // 比较距离
  return distance1Km < distance2Km
}

/**
 * 按最大宽度截断富文本标题，支持HTML标签嵌套，超出宽度自动补全标签并添加省略号
 *
 * @param title         原始富文本标题字符串（可包含HTML标签）
 * @param maxWidth      最大允许宽度（px）
 * @param charWidth     单个字符的基准宽度（px）
 * @param ellipsisWidth 省略号"..."的宽度（px）
 * @returns             { html, debug } html为截断后的富文本字符串，debug为调试信息
 *
 * 主要逻辑：
 * - 逐字符遍历title，遇到HTML标签时入栈/出栈，遇到文本时累加宽度
 * - 若累加宽度+省略号宽度超出maxWidth，则截断，补全未闭合标签，并在末尾加省略号
 * - 返回截断后的HTML和详细调试信息
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export function truncateTitleByWidth(title: string, maxWidth: number, charWidth: number, ellipsisWidth: number): { html: string, debug: any } {
  let width = 0
  let result = ''
  let truncated = false
  const stack = [] // 标签栈
  const debugSteps = []
  const regex = /(<\/?[^>]+>)|([^<]+)/g // 匹配HTML标签或纯文本
  let match
  let charIndex = 0
  let stopReason = ''
  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(title)) !== null) {
    const [, tag, text] = match
    if (tag) {
      if (tag.startsWith('</')) {
        // 结束标签，出栈
        const lastTag = stack.length ? stack[stack.length - 1] : null
        if (lastTag && tag.includes(lastTag.tagName)) {
          stack.pop()
          result += tag
          debugSteps.push({ type: 'endTag', tag, stack: [...stack] })
        }
      } else {
        // 开始标签，入栈
        const tagName = tag.match(/<(\w+)/)[1]
        stack.push({ tagName, tag })
        result += tag
        debugSteps.push({ type: 'startTag', tag, stack: [...stack] })
      }
    } else if (text) {
      for (let i = 0; i < text.length; i++) {
        const ch = text[i]
        const chWidth = estimateTextWidth(ch, charWidth)
        debugSteps.push({
          type: 'char',
          ch,
          chWidth,
          widthBefore: width,
          widthAfter: width + chWidth,
          maxWidth,
          charIndex,
        })
        if (width + chWidth + ellipsisWidth > maxWidth) {
          truncated = true
          stopReason = `超出maxWidth: width(${width}) + chWidth(${chWidth}) + ellipsisWidth(${ellipsisWidth}) > maxWidth(${maxWidth})`
          break
        }
        result += ch
        width += chWidth
        charIndex++
      }
      if (truncated) break
    }
  }
  // 截断后补齐标签
  if (truncated) {
    while (stack.length) {
      const { tagName } = stack.pop()
      result += `</${tagName}>`
    }
    result += '...'
  }
  // debug信息
  const debug = {
    totalWidth: width,
    maxWidth,
    truncated,
    stopReason: truncated ? stopReason : '未超出maxWidth',
    charIndex,
    steps: debugSteps,
    finalHtml: result,
  }
  // 没超出直接返回原文
  if (!truncated) {
    return { html: title, debug }
  }
  return { html: result, debug }
}

/**
 * 截断富文本 HTML 字符串到指定的可见字符数，并保证 HTML 结构完整。
 * 会在超出字符数后自动补齐未闭合的标签，并在末尾追加省略号（...）。
 *
 * @param {string} html - 需要截断的富文本 HTML 字符串
 * @param {number} limit - 允许显示的最大可见字符数
 * @returns {string} 截断后的 HTML 字符串，结构完整，超出部分以 ... 结尾
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export function truncateRichText(html, limit) {
  try {
    let textLength = 0 // 计数可见字符
    let truncated = '' // 存储最终的 HTML 结果
    const stack = [] // 用于存储未闭合的标签
    const regex = /(<\/?[^>]+>)|([^<]+)/g // 匹配 HTML 标签 或 纯文本
    let match

    // eslint-disable-next-line no-cond-assign
    while ((match = regex.exec(html)) !== null) {
      const [, tag, text] = match

      if (tag) {
        if (tag.startsWith('</')) {
          // 遇到结束标签，确保栈内有对应的开始标签
          const lastTag = stack.length ? stack[stack.length - 1] : null
          if (lastTag && tag.includes(lastTag.tagName)) {
            stack.pop() // 关闭匹配的开始标签
            truncated += tag
          }
        } else {
          // 遇到开始标签，入栈并添加到结果
          const tagName = tag.match(/<(\w+)/)[1] // 提取标签名称
          stack.push({ tagName, tag })
          truncated += tag
        }
      } else if (text) {
        // 遍历文本字符
        for (let i = 0; i < text.length; i++) {
          if (textLength < limit) {
            truncated += text[i]
            textLength++
          } else {
            // **停止后，先关闭所有 HTML 标签**
            while (stack.length) {
              const { tagName } = stack.pop()
              truncated += `</${tagName}>`
            }
            truncated += '...' // **最后在 HTML 结构外部追加 `...`**
            return truncated
          }
        }
      }
    }
    return truncated // **返回完整的 HTML 结构**
  } catch (error) {
    return html
  }
}

/**
 * 计算薪资标签的宽度
 *
 * @param showTags 薪资标签数组
 * @param salaryFontSize 薪资字体大小
 * @returns { width: number; debug: any } 薪资标签的宽度
 */
// eslint-disable-next-line sonarjs/cognitive-complexity
export function calculateSalaryWidth(showTags: any[], salaryFontSize: number): { width: number; debug: any } {
  if (!showTags || !showTags.length) {
    return {
      width: 0,
      debug: {
        hasSalary: false,
        salaryText: '',
        textWidth: 0,
        padding: 0,
      },
    }
  }

  const salaryTag = showTags.find(tag => tag.type === 1)
  if (!salaryTag) {
    return {
      width: 0,
      debug: {
        hasSalary: false,
        salaryText: '',
        textWidth: 0,
        padding: 0,
      },
    }
  }

  // 根据薪资文本长度估算宽度
  const text = salaryTag.name
  const charWidth = getCharWidth(salaryFontSize)
  const textWidth = estimateTextWidth(text, charWidth)

  // 加上padding和margin
  const padding = rpxToPx(16)
  const totalWidth = textWidth + padding

  const debug = {
    hasSalary: true,
    salaryText: text,
    textLength: text.length,
    fontSize: salaryFontSize,
    charWidth,
    textWidth,
    padding,
    totalWidth,
  }

  return { width: totalWidth, debug }
}

/**
 * 根据后端返回的图标宽高计算实际显示宽度
 * @param enterpriseIcon 后端返回的企业图标信息
 * @param displayHeightRpx 固定显示高度 40rpx
 * @param marginRightRpx 右边距 8rpx
 * @returns 计算后的显示宽度（px）
 */
export function calculateIconDisplayWidth(enterpriseIcon: { width: number; height: number, displayHeightRpx: number, marginRightRpx: number } | undefined): number {
  if (!enterpriseIcon?.width || !enterpriseIcon?.height) {
    return 0
  }
  // 屏幕总宽度
  const { windowWidth } = wx.getWindowInfo()
  const displayHeightRpx = enterpriseIcon.displayHeightRpx || 40 // 固定显示高度 40rpx
  const actualWidth = enterpriseIcon.width
  const actualHeight = enterpriseIcon.height

  // 根据固定高度和宽高比计算显示宽度
  const displayWidthRpx = (displayHeightRpx * actualWidth) / actualHeight

  // 转换为 px
  const widthPx = Math.floor((displayWidthRpx / 750) * windowWidth)

  // 加上 margin-right
  const marginRightPx = Math.floor(((enterpriseIcon.marginRightRpx) / 750) * windowWidth)
  return Math.floor(widthPx + marginRightPx)
}

/**
 * 根据期望职位tab信息判断卡片模式
 *
 * @param selectClassifyTabId 全职期望职位tab信息
 * @param selectSecondaryClassifyTabId 兼职期望职位tab信息
 * @param classifyTabClassify 所有期望职位tab列表
 * @returns { cardMode: number, debug: any } 卡片模式和调试信息
 */
export function getCardMode(
  selectClassifyTabId: any,
  selectSecondaryClassifyTabId: any,
  classifyTabClassify?: any[],
): { cardMode: number, debug: any } {
  // 期望职位混合mode
  const mixMode = (Array.isArray(classifyTabClassify) && classifyTabClassify.length > 0 && classifyTabClassify.every(it => it.mode == 1)) ? 1 : 2
  const { userState } = store.getState().storage
  let cardMode = 0
  const debugInfo: any = {
    reason: '',
    cardMode,
    mixMode,
    selectClassifyTabId,
    selectSecondaryClassifyTabId,
    classifyTabClassify,
  }
  if (!userState.login || classifyTabClassify?.length === 0) {
    cardMode = 2
    debugInfo.reason = '未登录和没有期望职位'
  } else if (selectClassifyTabId?.isRecommend) {
    // 如果选择的是全职的推荐
    cardMode = mixMode
    debugInfo.reason = '全职推荐'
  } else if (selectClassifyTabId?.isSpecialActivity === true && selectSecondaryClassifyTabId?.isInit === true) {
    // 如果选择的是兼职的推荐
    cardMode = mixMode
    debugInfo.reason = '兼职全部'
  } else if (selectClassifyTabId?.isSpecialActivity === false && !selectClassifyTabId?.isRecommend) {
    // 如果选择的是全职的工种
    cardMode = selectClassifyTabId?.mode || 2
    debugInfo.reason = '全职工种'
  } else if (selectClassifyTabId?.isSpecialActivity === true && !selectSecondaryClassifyTabId?.isInit) {
    // 如果选择的是兼职的工种
    cardMode = selectSecondaryClassifyTabId?.mode || 2
    debugInfo.reason = '兼职工种'
  } else {
    cardMode = 2
    debugInfo.reason = '默认'
  }

  debugInfo.cardMode = cardMode
  return { cardMode, debug: debugInfo }
}
