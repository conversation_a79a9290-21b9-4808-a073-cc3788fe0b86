/*
 * @Date: 2022-04-02 09:15:38
 * @Description: 应用启动
 */

import { store, actions, storage, dispatch } from '@/store/index'
import { login, location } from '@/utils/helper/index'
import { phoneType } from '@/utils/tools/validator/index'
import miniConfig from '@/miniConfig/index'
import { saveAccount } from './helper/login/index'
import { asyncRequest } from './request/utils'
import { urlEncodeParams } from './tools/common/index'
import { pageCodeArr } from './helper/resourceBit/pageCode'
import { changeUserRole } from './helper/member/communicate'
import { getResumeProcessPath } from './helper/resume/utils'
import { getBaseConfig, getStaticRiskData } from './helper/common/index'
import { getDialogConfig } from './helper/dialog/index'

/** 以下场景值启动不跳页面（启动路由为 /pages/index/index时） */
const filterScene = [1007, 1008, 1010, 1011, 1012, 1013, 1025, 1031, 1032, 1034, 1036, 1047, 1048, 1049]

/** 高端手机初始化 */
async function highEndMobilePhone() {
  // 初始化本地缓存
  dispatch(actions.storageActions.initState())
  // 初始化用户信息
  login.setUserInfo()
  // 初次进页面请求城市接口
  setTimeout(() => {
    wx.$.l.getAreaTreeData()
  }, 3000)

  const LaunchData = wx.getLaunchOptionsSync()
  if (miniConfig.token != 'gdh' && LaunchData.path != 'subpackage/member/webFirmAuth/index') {
    // 用户启动app进行定位
    const L_IS_GEO_AUTH = storage.getItemSync('L_IS_GEO_AUTH')
    L_IS_GEO_AUTH == 1 ? location.citySwitchAndLocal() : location.getUserIpLocation(true)
  }
  // 获取用户标识，比如openId等
  await login.getWechatAuthInfo(true)
  // 延迟执行im登录
  setTimeout(() => {
    wx.$.l.getTimObj()
    wx.$.l.reTimLogin()
  }, 2000)
}

/** 低端手机初始化 */
function lowEndCellPhone() {
  setTimeout(() => {
    store.dispatch(actions.storageActions.initState())
    // 初始化本地缓存
    setTimeout(() => {
      // 初始化用户信息
      login.setUserInfo()
      // 初次进页面请求城市接口
      setTimeout(() => {
        wx.$.l.getAreaTreeData()
      }, 3000)
      setTimeout(async () => {
        const LaunchData = wx.getLaunchOptionsSync()
        if (miniConfig.token != 'gdh' && LaunchData.path != 'subpackage/member/webFirmAuth/index') {
          // 用户启动app进行定位
          const L_IS_GEO_AUTH = storage.getItemSync('L_IS_GEO_AUTH')
          L_IS_GEO_AUTH == 1 ? location.citySwitchAndLocal() : location.getUserIpLocation(true)
        }
        // 获取用户标识，比如openId等
        await login.getWechatAuthInfo(true)
        setTimeout(() => {
          wx.$.l.getTimObj()
          wx.$.l.reTimLogin()
        }, 2000)
      }, 1000)
    }, 1000)
  }, 3000)
}

export async function init() {
  // 百度无性能问题，可以直接初始化
  if (ENV_IS_SWAN) {
    highEndMobilePhone()
  } else {
    // 根据机型选择初始化
    const type = phoneType()
    if (type) {
      highEndMobilePhone()
    } else {
      lowEndCellPhone()
    }
  }
  const { userId } = store.getState().storage.userState
  userId && wx.$.collectEvent.config({ user_unique_id: userId })
  // 基础配置
  getBaseConfig({ isAll: true })
  // 清理首页弹窗展示缓存
  storage.removeSync('indexShowModal')
  // 清理用户选择的定位信息
  // await dispatch(actions.storageActions.removeItem('recruitLocaltion'))
  setTimeout(() => {
    // 广告预加载
    const { recruitUnitId, recruitDetailsUnitId, callAndBrowseUnitId } = miniConfig.advert
    wx.preloadAd && wx.preloadAd([{ unitId: recruitUnitId, type: 'custom' }, { unitId: recruitDetailsUnitId, type: 'custom' }, { unitId: callAndBrowseUnitId, type: 'custom' }])
  }, 5000)
  // 执行一次已经登录了要存下档前登录账号信息
  saveAccount()
  userCaptureScreen()
  getShareConfig()
}

/** 监听用户截屏 */
function userCaptureScreen() {
  wx.onUserCaptureScreen(() => {
    wx.$.model.screenshotShare({ intercept: true })
  })
}

/** 获取分享配置 */
function getShareConfig() {
  if (['findjzgr', 'gdh', 'zyqg'].includes(ENV_SUB)) {
    asyncRequest({
      url: `https://cdn.yupao.com/json/mini_share_config.json?time=${Date.now()}`,
      data: { wechat_token: miniConfig.token },
      header: { 'content-type': 'application/json' },
      method: 'GET',
      dataType: 'json',
      responseType: 'text',
    }).then(res => {
      dispatch(actions.configActions.setState({ shareConfig: res?.data || {} }))
    })
  }
}
/** 微信、百度自动更新逻辑 */
export function autoUpdate() {
  const { query } = wx.getLaunchOptionsSync()
  const isAppPay = query.payProductChannel == 'APP' && ENV_SUB == 'gdh' // 如果是app支付跳转小程序（鱼泡招工）不自动检测更新
  if (isAppPay || (!ENV_IS_WEAPP && !ENV_IS_SWAN)) {
    return
  }
  if (wx.canIUse('getUpdateManager')) {
    const updateManager = wx.getUpdateManager()
    /** 检查是否有新版本发布 */
    try {
      updateManager.onCheckForUpdate(res => {
        if (res.hasUpdate) {
          /** 有新版本，开始静默下载新版本，准备更新 */
          updateManager.onUpdateReady(() => {
            /** 开始更新 */
            updateManager.applyUpdate()
          })
        }
      })
      // eslint-disable-next-line no-empty
    } catch (e) { }
  }
}

/** 启动埋点 */
export const buryingPoint = (option) => {
  if (!option) {
    return
  }
  // 延迟执行，等待页面初始化完成
  setTimeout(async () => {
    const jump_url = `${option.path}${urlEncodeParams(option.query)}`
    // 直传后端（日活）
    wx.$.collectEvent.javaEvent('appLaunch', {
      start_source: `${option.scene}`,
      start_type: '1',
      channel_message_id: option.query.reach_msg_id || '',
      jump_url,
    })
    const c = await getStaticRiskData()
    // 直传后端（风险监测）
    wx.$.collectEvent.javaEvent('app_launch_r_c', { c })
  }, 1500)
}

/** 获取小程序启动参数 */
export const getQueryParam = (param): any => {
  let { scene, ...query } = param
  if (scene) {
    scene = decodeURIComponent(scene)
    scene.split('&').forEach((item) => {
      const [key, value] = item.split('=')
      query[key] = value
    })
  }
  return query
}

/** 埋点 */
export function collectShareLaunch(params) {
  const userState = storage.getItemSync('userState')
  params.sharer_id && params.sharer_id != '-99999' && wx.$.collectEvent.config({ share_id: params.sharer_id })
  wx.$.collectEvent.event('shareLaunch', {
    user_id: userState.userId,
    ...params,
  })
}

/** 判断路由参数是否存在 shareAgg 参数，和不包含有其他额外的参数 */
export const isShareParam = (query, keys = []) => {
  const { shareAgg } = query || {}
  const pages = getCurrentPages ? getCurrentPages() || [] : []
  if (pages.length > 1) {
    return false
  }
  if (!shareAgg) {
    return false
  }
  if (keys.length) {
    return !keys.some(key => query[key])
  }
  return true
}

/** 用户启动时判断是否需要进入简历完善流程 */
export const isNeedResumePerfect = async (options) => {
  wx.$.u.wait(500)
  const isShow = await wx.$.u.cache('app', 'show')
  const { userChooseRole, userState } = store.getState().storage
  const { isResumeProcess } = store.getState().storage.common || {}
  if (isShow || !userState.login || userChooseRole == 1 || !isResumeProcess || options.path != 'pages/index/index') {
    return false
  }
  const resumeProcess = await dispatch(actions.resumeActions.getResumeConfig())
  const path = getResumeProcessPath(resumeProcess.item, resumeProcess.module)
  if (path) {
    wx.$.r.push({ path })
    return true
  }
  return false
}

/** 处理onShow页面重定向逻辑 */
export const handleOnShowRedirect = async (options) => {
  const { login } = store.getState().storage.userState
  const currentPage = wx.$.r.getCurrentPage()
  let param = wx.$.u.deepClone(options)
  let noUpdateRole = false
  if (param.query && param.query.scene) {
    const scene = decodeURIComponent(options.query.scene)
    scene.split('&').forEach((item) => {
      const [key, value] = item.split('=')
      param.query[key] = value
    })
  }

  //! 强制实名
  // eslint-disable-next-line max-len
  if (options.query.payProductChannel != 'APP' && login && (!currentPage?.route || (currentPage?.route.indexOf('subpackage/member/realname/index') == -1 && currentPage?.route.indexOf('subpackage/mp-ecard-sdk/index/index') == -1 && currentPage?.route.indexOf('subpackage/userauth/auth/index') == -1))) {
    const res = await wx.$.javafetch['POST/account/v1/user/change/realName/mandatory/realNameVerify']()
    if (res.data.verifyResult) {
      wx.$.r.reLaunch({ path: '/subpackage/member/unbindRealname/mandatory_certification/index', query: { unbindType: 1 } })
      param.isRedirect = true
      // 强制实名不需要更新角色
      noUpdateRole = true
    }
  }

  //! 二维码分享进入
  const shareAgg = param.shareAgg || param.query.shareAgg
  if (shareAgg && !param.isRedirect) {
    // 微信环境
    const res = await wx.$.javafetch['GET/share/v1/setting/queryShareAggParams']({
      trackSeed: shareAgg,
    }, { hideMsg: true }).catch(err => err)
    if (!res.error) {
      if (param.query && param.query.shareAgg) {
        param.query = res.data
        /** 埋点 */
        collectShareLaunch({
          track_seed: res.data.track_seed,
          refid: res.data.refid,
          sharer_id: res.data.refid,
          userAcq: param.userAcq || '-99999',
        })
      } else {
        param = res.data
      }

      if (param.path) {
        if (res.data.refid) {
          dispatch(actions.storageActions.setItem({ key: 'sourceCode', value: res.data.refid }))
        }
        if (res.data.track_seed) {
          dispatch(actions.storageActions.setItem({ key: 'track_seed_share', value: res.data.track_seed }))
        }
        /** 设置 param设置isRedirect代表是否跳转了路由 */
        param.isRedirect = true
        if (res.data?.detailId) {
          if (res.data.type == 'resume') {
            res.data.uuid = res.data.detailId
          } else {
            res.data.id = res.data.detailId
          }
        }
        wx.$.r.reLaunch({
          path: `/${param.path}`,
          query: res.data,
        })
      }
    }
  }

  //! 冷启动时，判断用户是否需要进入简历完善流程
  if (!param.isRedirect) {
    param.isRedirect = await isNeedResumePerfect(options)
  }

  //! 设置角色
  const { userChooseRole, userState } = store.getState().storage
  const { path, scene, query } = options
  // 会员中心页面不做处理
  if ((!currentPage || currentPage.route !== options.path || options.path == 'pages/msg-page/index') && path !== 'pages/ucenter/index' && !noUpdateRole) {
    if (!param.isRedirect && userChooseRole === 1 && path === 'pages/index/index' && !filterScene.includes(scene)) {
      param.isRedirect = true

      /* setTimeout(() => {
        wx.$.r.reLaunch({ path: '/subpackage/recruit/published/index' query})
      }) */

      // const isStore = storage.getItemSync('userState')
      let isReLaunch = false
      // const isAbc = await wx.$.u.getAbUi('bossLaunchPath', 'default')
      if (userState && userState.login) {
        const { data: resData } = await wx.$.javafetch['POST/account/v1/role/getLaunchPage']().catch(err => err)
        if (resData && resData.launchPage == 1 && resData.nowRole == 1) {
          isReLaunch = true
          setTimeout(() => {
            wx.$.r.reLaunch({ path: '/pages/resume/index' })
          })
        }
      }
      // }
      if (!isReLaunch) {
        setTimeout(() => {
          wx.$.r.reLaunch({ path: '/subpackage/recruit/published/index' })
        })
      }
    } else {
      const cuPagefig = pageCodeArr.find(item => item.path === path)
      const pageRole = (cuPagefig && cuPagefig.pageRole) || (query.user_rloe && Number(query.user_rloe))
      pageRole && pageRole !== userChooseRole && changeUserRole(pageRole, userState.login)
    }
  }
  return param
}

/** 处理启动参数 */
export const handleLaunchOption = async (options) => {
  let refId = ''
  let source = ''
  let track_seed = ''
  let video_dialog = '0'
  const optionParams = { ...options }
  if (optionParams.referrerInfo) {
    dispatch(actions.storageActions.setItem({ key: 'referrerInfo', value: optionParams.referrerInfo }))
  }
  dispatch(actions.configActions.setState({ launchOptions: { path: options.path, scene: options.scene } }))
  if (optionParams.query.scene) {
    const scene = decodeURIComponent(optionParams.query.scene)
    scene.split('&').forEach((item) => {
      const [key, value] = item.split('=')
      optionParams.query[key] = value
    })
  }
  const setParams = (query) => {
    refId = query.refid || ''
    source = query.source || ''
    track_seed = query.track_seed || ''
    video_dialog = query.video_dialog || '0'
  }

  if (ENV_IS_WEAPP) {
    setParams(options.query)
  } else if (ENV_IS_SWAN) {
    const pages = getCurrentPages()
    setParams(pages[0].options)
  }

  const newUserToastZhuli = storage.getItemSync('newUserToastZhuli') || { video_dialog: 0, isNewUser: 0 }
  const qrCodeUrl = options.query.q ? decodeURIComponent(options.query.q) : ''
  const [, params = ''] = qrCodeUrl.split('?')

  if (params) {
    params.split('&').forEach((item) => {
      const [key, value] = item.split('=')
      if (key === 'qrid') {
        dispatch(actions.storageActions.setItem({ key: 'track_seed_share', value }))
      } else if (key === 'track_seed') {
        track_seed = value
      } else if (key === 'share_source') {
        source = value
      } else if (key === 'refid') {
        refId = value
      } else if (key === 'video_dialog') {
        video_dialog = value
      }
    })
  }

  if (refId && refId !== '0') {
    dispatch(actions.storageActions.setItem({ key: 'sourceCode', value: refId }))
  }
  if (track_seed) {
    dispatch(actions.storageActions.setItem({ key: 'track_seed_share', value: track_seed }))
    if (source) {
      dispatch(actions.storageActions.setItem({ key: 'source_share', value: source }))
    }
  }
  if (video_dialog) {
    dispatch(actions.storageActions.setItem({ key: 'newUserToastZhuli', value: { ...newUserToastZhuli, video_dialog } }))
  }

  // 判断清除红包活动的唯一标识
  if (source && !options?.query?.isShareRedEnvelope) {
    dispatch(actions.otherActions.setState({ sighInRedPackTitle: '' }))
  }
}

/** 冷启动获取｜更新配置数据 */
export const getLaunchConfig = () => {
  // 获取工种数据
  wx.$.l.getClassTreeData(true)
  // 弹窗配置
  getDialogConfig(true)
}
