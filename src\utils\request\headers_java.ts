/*
 * @Date: 2022-06-22 18:01:49
 * @Description: header头部
 */
import { app } from '@/config/index'
import { store, storage } from '@/store/index'
import miniConfig from '@/miniConfig/index'
import CryptoJS from '@/lib/crypto/index'
import { common } from '@/utils/tools/index'
import { encrypt, getSystemInfoSync } from '../tools/common/index'
import { getHeaderSeries, getHeadersModel, transFromRuntime } from './utils'

const platformList = {
  /** 微信小程序 */
  weapp: 'wx_mini',
  /** 百度小程序 */
  swan: 'baidu_mini',
  /** 字节小程序 */
  tt: 'bd_mini',
}

/** 服务器的签名秘钥 */
// const SECRET_SIGN = '8k&^$Hsk1?kkcj12^99K1ia'
const SECRET_SIGN = '*js1(Uc_m12j%hsn#1o%cn1'

/** json对象key排序 */
// eslint-disable-next-line consistent-return
function sortObj(obj) {
  if (obj === null) {
    return obj
  }
  if (Array.isArray(obj)) {
    return [...obj].reduce((acc, item) => {
      if (typeof item === 'object') {
        acc.push(sortObj(item))
      } else {
        acc.push(item)
      }
      return acc
    }, [])
  }
  if (typeof obj === 'object') {
    return Object.keys(obj)
      .sort()
      .reduce((acc, key) => {
        if (typeof obj[key] === 'object') {
          // eslint-disable-next-line no-param-reassign
          acc[key] = sortObj(obj[key])
        } else {
          // eslint-disable-next-line no-param-reassign
          acc[key] = obj[key]
        }
        return acc
      }, {})
  }
}

/** json对象转url的params */
// eslint-disable-next-line consistent-return
function stringifyObj(t) {
  if (t == null) {
    return t
  }
  // 数组
  if (Array.isArray(t)) {
    const list = [...t].reduce((acc, item) => {
      if (typeof item === 'object') {
        acc.push(sortObj(item))
      } else {
        // eslint-disable-next-line no-param-reassign
        acc += item
      }
      return acc
    }, [])
    return JSON.stringify(list)
  }
  // 是对象 非数据
  if (typeof t === 'object') {
    return Object.keys(t)
      .sort()
      .reduce((acc, key) => {
        if (typeof t[key] === 'object') {
          return `${acc}${key}=${JSON.stringify(t[key])}&`
        }
        return `${acc}${key}=${t[key]}&`
      }, '')
  }
}

/** 参数加密 */
async function getSign(params, timestamp, nonce) {
  const newParams = sortObj({ ...params, timestamp, nonce })
  const signString = stringifyObj(newParams) + SECRET_SIGN
  const c = await CryptoJS()
  return c.SHA256(signString).toString()
}
/**
 * 获取header头
 * @param customHead 自定义header
 * @param requestData 请求参数
 * @returns headers
 */
export async function getHeaders(requestData = {}, customHead = {}) {
  const trackSeed = storage.getItemSync('track_seed_share')
  const refid = storage.getItemSync('sourceCode')
  const { userChooseRole, userState: userInfo } = store.getState().storage
  const nonce = Math.round(Math.random() * 999999)
  const timestamp = `${Math.floor(new Date().getTime())}`
  // const timestamp = `${Math.floor(new Date().getTime() / 1000)}`
  const sign = await getSign(requestData, timestamp, nonce)
  const gps = await encrypt(store.getState().storage.userLocation, 'header')
  const systemInfo = common.getSystemName()
  const model = getHeadersModel()
  const { token } = store.getState().user.webTokenData
  /* 参数请求头 */
  return {
    occversion: 2,
    /** content-type */
    'content-type': 'application/json',
    /** 分享小程序的trackSeed */
    trackSeed,
    /** 用户分享的refid存入本地 */
    refid,
    /** 请求参数传输类型 */
    requestType: 'form',
    /** 来源端 1-鱼泡网 */
    business: 'YPZP',
    /** 版本号 */
    version: app.REQUEST_VERSION,
    /** 版本号 */
    versionmini: app.versionmini,
    /** 版本号 */
    wechat_token: miniConfig.appid,
    /** 终端 */
    system_type: getHeaderSeries(),
    /** 令牌时间 */
    token_time: userInfo.tokenTime || '',
    /** 用户uuid */
    uuid: userInfo.uuid || '',
    /** 跳系统升级页面用 */
    uid: userInfo.userId || '',
    /** 版本号 */
    mid: userInfo.userId || '',
    /** 请求时间 */
    time: timestamp,
    // ! 勿删以下header参数
    /** 签名时间戳 */
    timestamp,
    /** 应用版本，应用版本。 */
    appVersion: app.REQUEST_VERSION,
    /** 操作系统 ios, android, windows, macos, linux */
    system: systemInfo.system,
    /** 系统版本 */
    systemVersion: systemInfo.systemVersion,
    /** 随机数，[1, 999999]之间的整数 */
    nonce,
    /** wx_mini: 微信小程序 bd_mini: 字节小程序 baidu_mini: 百度小程序 qq_mini: QQ小程序 */
    platform: platformList[ENV_MINI_TYPE],
    /** 用户登录态token */
    token: token || userInfo.token || '',
    /** 用户数据签名 */
    sign,
    /** 签名版本 */
    signversion: 1,
    hybrid: 'NO',
    /** 操作系统 */
    os: systemInfo.system,
    runtime: transFromRuntime(),
    channel: getSystemInfoSync().platform,
    osVersion: getSystemInfoSync().version,
    packagename: miniConfig.appid,
    /** 当前角色 1 B端、老板、找工人、招工  2 C端、工人、找工作、找活 */
    userrole: userChooseRole || userInfo.role,
    runtimeversion: ENV_IS_SWAN ? app.VERSION : (wx.getAccountInfoSync()?.miniProgram?.version || systemInfo.systemVersion),
    // packageVersion: app.REQUEST_VERSION,
    /** header包版本: https://w3nu1yaadv.feishu.cn/wiki/wikcnLJEaiaUnob6aKEqTtA4Muh/ */
    packageversion: app.REQUEST_VERSION,
    /** 业务区分 */
    reqsource: 'YPZP',
    /** 新增参数 */
    brand: getSystemInfoSync().brand,
    model: model.indexOf('iPad') > -1 ? 'UNKONW' : model,
    // encrypted: '1.0',
    gps,
    // openid
    openid: storage.getItemSync('loginAuthData')?.openid || '',
    // unionid
    unionid: storage.getItemSync('loginAuthData')?.unionid || '',
    ...customHead,
  }
}
