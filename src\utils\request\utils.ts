/*
 * @Author: chenlong
 * @Date: 2021-10-20 16:11:03
 * @Description: 切换新老接口路径
 */

import { app } from '@/config/index'
import { getSystemInfoSync } from '../tools/common/index'
import { actions, dispatch, messageQueue, storage, store } from '@/store/index'

// ios机型
const iosModels = {
  'iPod5,1': 'iPod Touch 5',
  'iPod7,1': 'iPod Touch 6',
  'iPhone3,1': 'iPhone 4',
  'iPhone3,2': 'iPhone 4',
  'iPhone3,3': 'iPhone 4',
  'iPhone4,1': 'iPhone 4s',
  'iPhone5,1': 'iPhone 5',
  'iPhone5,2': 'iPhone 5',
  'iPhone5,3': 'iPhone 5c',
  'iPhone5,4': 'iPhone 5c',
  'iPhone6,1': 'iPhone 5s',
  'iPhone6,2': 'iPhone 5s',
  'iPhone7,2': 'iPhone 6',
  'iPhone7,1': 'iPhone 6 Plus',
  'iPhone8,1': 'iPhone 6s',
  'iPhone8,2': 'iPhone 6s Plus',
  'iPhone9,1': 'iPhone 7 (CDMA)',
  'iPhone9,3': 'iPhone 7 (GSM)',
  'iPhone9,2': 'iPhone 7 Plus (CDMA)',
  'iPhone9,4': 'iPhone 7 Plus (GSM)',
  'iPhone10,1': 'iPhone 8',
  'iPhone10,4': 'iPhone 8',
  'iPhone10,5': 'iPhone 8 Plus',
  'iPhone10,2': 'iPhone 8 Plus',
  'iPhone10,3': 'iPhone X',
  'iPhone10,6': 'iPhone X',
  'iPhone11,2': 'iPhone XS',
  'iPhone11,6': 'iPhone XS MAX',
  'iPhone11,8': 'iPhone XR',
  'iPhone12,1': 'iPhone 11',
  'iPhone12,3': 'iPhone 11 Pro Max',
  'iPhone12,5': 'iPhone 11 Pro',
  'iPhone12,8': 'iPhone SE (2nd)',
  'iPhone13,1': 'iPhone 12 mini',
  'iPhone13,2': 'iPhone 12',
  'iPhone13,3': 'iPhone 12 Pro',
  'iPhone13,4': 'iPhone 12 Pro MAX',
  'iPhone14,4': 'iPhone 13 mini',
  'iPhone14,5': 'iPhone 13',
  'iPhone14,2': 'iPhone 13 Pro',
  'iPhone14,3': 'iPhone 13 Pro Max',
  'iPhone14,6': 'iPhone SE 3',
  'iPhone14,7': 'iPhone 14',
  'iPhone14,8': 'iPhone 14 Plus',
  'iPhone15,2': 'iPhone 14 Pro',
  'iPhone15,3': 'iPhone 14 Pro Max',
  'iPhone15,4': 'iPhone 15',
  'iPhone15,5': 'iPhone 15 Plus',
  'iPhone16,1': 'iPhone 15 Pro',
  'iPhone16,2': 'iPhone 15 Pro Max',
  'iPad2,1': 'iPad 2',
  'iPad2,2': 'iPad 2',
  'iPad2,3': 'iPad 2',
  'iPad2,4': 'iPad 2',
  'iPad3,1': 'iPad 3',
  'iPad3,2': 'iPad 3',
  'iPad3,3': 'iPad 3',
  'iPad3,4': 'iPad 4',
  'iPad3,5': 'iPad 4',
  'iPad3,6': 'iPad 4',
  'iPad4,1': 'iPad Air',
  'iPad4,2': 'iPad Air',
  'iPad4,3': 'iPad Air',
  'iPad5,3': 'iPad Air 2',
  'iPad5,4': 'iPad Air 2',
  'iPad2,5': 'iPad Mini',
  'iPad2,6': 'iPad Mini',
  'iPad2,7': 'iPad Mini',
  'iPad4,4': 'iPad Mini 2',
  'iPad4,5': 'iPad Mini 2',
  'iPad4,6': 'iPad Mini 2',
  'iPad4,7': 'iPad Mini 3',
  'iPad4,8': 'iPad Mini 3',
  'iPad4,9': 'iPad Mini 3',
  'iPad5,1': 'iPad Mini 4',
  'iPad5,2': 'iPad Mini 4',
  'iPad6,7': 'iPad Pro',
  'iPad6,8': 'iPad Pro',
  'AppleTV5,3': 'Apple TV',
  i386: 'Simulator',
  x86_64: 'Simulator',
}

/*
 * @Description: 上传图片的header
 */

/** request 服务器请求状态值 */
export const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  405: '请求方法不被允许。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
}

/** request异常，不弹出toast白名单 */
export const codeMessageWhite = [404]

/**
 * @description: 获取头部信息中，携带的平台别名
 * @param {*}
 * @return string 携带的平台参数别名
 */
export const getHeaderSeries = (): string => {
  if (ENV_IS_WEAPP) {
    return 'wechat'
  }

  if (ENV_IS_SWAN) {
    return 'baidu'
  }
  return 'weixin'
}

/**
 * @description: 返回完整的接口url
 * @param {string} url
 * @return {string}
 */
export const getUrl = (url: string): string => {
  return app.REQUEST_URL.slice(0, app.REQUEST_URL.length - 1) + url
}

/** 封装wx.request ，让其可以通过promise风格调用 */
export const asyncRequest = (config) => {
  return new Promise((resolve, reject) => {
    wx.request({
      ...config,
      success(res) {
        resolve(res)
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

export const transFromRuntime = () => {
  if (ENV_IS_SWAN) {
    return 'BAIDU_MINI'
  } if (ENV_IS_WEAPP) {
    return 'WX_MINI'
  }

  return 'WX_MINI'
}

/** 不需要跳转到登录页的接口白名单 */
export const loginWhite = [
  /** 弹框埋点的接口 */
  '/cms/popup/v1/showReport',
  /** 【完】订阅招工输入框获取标签列表 */
  '/job/v2/search/words/input',
  '/reach/v1/config/subJob/getAssociationalLabelList',
  /** 获取中间号引导视频配置 */
  '/reach/v1/privacyTel/config/guideVideoSetting',
  /** 工厂发布流程弹窗控制接口 */
  '/job/app/factoryRecruit/getPopType',
  /** 登录页里面web授权接口 */
  '/account/v1/login/mini/sunCodeLoginConfirm',
]

const errcode = [
  400, // 参数错误
  403, // 拒绝请求
  404, // 未找到资源
  429, // 请求限流
  430, // 签名错误
  500, // 服务器内部错误
  502, // 网关错误
  503, // 服务不可用
  504, // 请求超时
  509, // 超时执行
]
/** 生成curl */
export const getCurlString = (config) => {
  const { url, headers, requestData, method } = config
  let cmd = `curl -X ${method.toUpperCase()} `
  Object.keys(headers || {}).forEach((ky) => {
    cmd += `-H '${ky}:${headers[ky]}' `
  })
  cmd += `-d '${JSON.stringify(requestData)}' `
  cmd += `'${url}'`
  return cmd
}

/**
 * @description IOS model转换值
 */
export const getHeadersModel = () => {
  const { model, platform } = getSystemInfoSync()
  if (platform !== 'ios') {
    return model
  }
  // 如果model中包含<>符号，提取<>中的内容
  const reg = /<([^>]*)>/
  const match = model.match(reg)
  if (match && match[1] && iosModels[match[1]]) {
    // match[1]中如果有p，就替换为P (小写替换为大写)
    const modelY = match[1].replace('p', 'P')
    return iosModels[modelY]
  }
  return model.length > 32 ? '' : model
}

// 处理异常，跳转系统维护页面(服务宕机)
export const handleSystemMaintenance = async (code) => {
  try {
    await messageQueue((state) => state.storage.common && state.storage.common.sysmainrequest)
    dispatch(actions.storageActions.setCommonItem({ sysmainrequest: false }))
    const sysmaincount = storage.getItemSync('sysmaincount')
    const { sysmainshow } = store.getState().storage.common || {}
    if (sysmainshow) {
      dispatch(actions.storageActions.setCommonItem({ sysmainrequest: true, sysmainshow: false }))
      return true
    }
    dispatch(actions.storageActions.setCommonItem({ sysmainshow: true }))
    let isOk = false
    const nCode = Number(code)
    if (nCode >= 500 && nCode < 600) {
      if (sysmaincount < 2) {
        storage.setItemSync('sysmaincount', sysmaincount + 1)
      }
      if (sysmaincount + 1 >= 2) {
        const pages = getCurrentPages()
        if (pages.length) {
          const page = pages[pages.length - 1]
          if (page.route.indexOf('subpackage/tools-page/system-maintenance/index') >= 0) {
            dispatch(actions.storageActions.setCommonItem({ sysmainrequest: true, sysmainshow: false }))
            return true
          }
        }
        const stData = await wx.$.l.fetchStandbyData()
        const { isopen } = stData || {} as any
        if (isopen) {
          dispatch(actions.storageActions.setCommonItem({ sysmainrequest: true }))
          setTimeout(() => {
            dispatch(actions.storageActions.setCommonItem({ sysmainshow: false }))
          }, 5000)
          wx.$.r.reLaunch({ path: '/subpackage/tools-page/system-maintenance/index' })
          isOk = true
        } else {
          dispatch(actions.storageActions.setCommonItem({ sysmainshow: false }))
          storage.setItemSync('sysmaincount', 0)
        }
      } else {
        dispatch(actions.storageActions.setCommonItem({ sysmainshow: false }))
      }
      dispatch(actions.storageActions.setCommonItem({ sysmainrequest: true }))
      return isOk
    }
    dispatch(actions.storageActions.setCommonItem({ sysmainrequest: true, sysmainshow: false }))
    return false
  } catch (err) {
    dispatch(actions.storageActions.setCommonItem({ sysmainrequest: true, sysmainshow: false }))
    return false
  }
}
