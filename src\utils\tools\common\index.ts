/*
 * @Date: 2022-02-12 17:27:46
 * @Description: 公共工具方法
 */

import { MiniShareInfoPathParams } from '../type'
import CryptoJS from '@/lib/crypto/index'
import { app } from '@/config/index'
import { REPORT_ENCRYPT_IV, REPORT_ENCRYPT_KEY } from '@/config/app'

/** 获取系统信息
 * mid: 设备平台: model, pc, ipad
 */
export const getSystemInfoSync = (() => {
  let data = null
  return () => {
    if (data === null) {
      data = wx.getSystemInfoSync()
      if (data && data.platform) {
        data.platform = `${data.platform}`.toLocaleLowerCase()
        data.mid = `${data.platform}`.toLocaleLowerCase()
        if (data.platform === 'ios' || data.platform === 'android') {
          data.mid = 'mobile'
        } else if (data.platform === 'mac' || data.platform === 'windows') {
          data.mid = 'pc'
        }
      }
    }
    return data
  }
})()

/** 获取系统名称 ios, android */
export const getSystemName = (() => {
  let systemName = null
  return () => {
    if (systemName == null) {
      const { system, version, model } = wx.getSystemInfoSync()
      const systems = `${system}`.toLocaleUpperCase().split(' ')
      systemName = {
        system: systems[0],
        model: model ? `${model}`.toLocaleLowerCase() : '',
        systemVersion: systems[1] || version,
      }
    }
    return systemName
  }
})()

/** try catch */
export async function tryPromise<T>(promise: T, defaultValue: any = undefined): Promise<T> {
  try {
    return await promise
  } catch (err) {
    console.error('err', err, promise)
    return defaultValue === undefined ? err : defaultValue
  }
}

/** 获取元素信息,如果没有获取到会在一段间隔后再次回去，如果三次没获取到，则返回null */
export function getDom(idName, count = 0, promiseCtx = null, resolveCtx = null) {
  const handler = (promise, resolve) => {
    if (ENV_IS_WEAPP || ENV_IS_SWAN) {
      wx.nextTick(() => {
        const query = this && this.createSelectorQuery ? this.createSelectorQuery() : wx.createSelectorQuery()
        query
          .select(idName)
          .boundingClientRect((res) => {
            if (res) {
              resolve(res)
            } else if (count < 3) {
              setTimeout(() => {
                getDom(idName, count + 1, promise, resolve)
              }, 200 * (count + 1))
            } else {
              resolve(null)
            }
          })
          .exec()
      })
    }
  }
  if (promiseCtx) {
    handler(promiseCtx, resolveCtx)
    return promiseCtx
  }
  let resolveFunc
  const promise = new Promise((resolve) => {
    resolveFunc = resolve
  })
  handler(promise, resolveFunc)
  return promise
}

/** 获取菜单按钮（右上角胶囊按钮）的布局位置信息 */
export const getMenuButtonBoundingClientRect = (() => {
  let value
  return () => {
    if (value && value.top && value.height) {
      return value
    }
    value = wx.getMenuButtonBoundingClientRect()
    return {
      ...value,
      width: value.width >= 120 ? 120 : value.width,
    }
  }
})()

/**
 * 获取胶囊的高度以及加上传入的高度
 * @param otherHeight - 额外的高度
 * @param isCustomHeader - 是否是custom-header组件
 * */
export const getHeaderHeight = (otherHeight = '0px', isCustomHeader = false): string => {
  const clientRect = getMenuButtonBoundingClientRect()
  return `calc(${clientRect.top + (clientRect?.height || 0)}px + ${isCustomHeader ? '8rpx' : '0px'} + ${otherHeight})`
}

/** 把一维数组拆分成二维数组，数组里面的数组长度可自己设置 */
export function toSplitArr(baseArray: any[], n: number) {
  const len = baseArray.length
  // n 假设每行显示N个
  const lineNum = len % n === 0 ? len / n : Math.floor(len / n + 1)
  const res: any[] = []
  for (let i = 0; i < lineNum; i += 1) {
    // slice() 方法返回一个从开始到结束（不包括结束）选择的数组的一部分浅拷贝到一个新数组对象。且原始数组不会被修改。
    const temp = baseArray.slice(i * n, i * n + n)
    res.push(temp)
  }
  return res
}

/**
 * @return: string uuid
 * @description: 获取uuid
 */
export const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    // eslint-disable-next-line no-bitwise
    const r = (Math.random() * 16) | 0
    // eslint-disable-next-line no-bitwise
    const v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * @params: params: {key: value}
 * @return: string 序列化之后的参数
 * @description: 将对象转为url参数 ?a=1&b=2
 */
export const urlEncodeParams = (params: MiniShareInfoPathParams, isp?): string => {
  let urlParams = ''
  Object.keys(params)?.forEach((item, index) => {
    urlParams += `${!index && !isp ? '?' : '&'}${item}=${params[item]}`
  })
  return urlParams
}

/** 获取url路由参数 返回json对象 */
export const urlDecodeParams = (url: string): MiniShareInfoPathParams => {
  const params: MiniShareInfoPathParams = {}
  const paramsStr = url.split('?')[1]
  if (paramsStr) {
    const paramsArr = paramsStr.split('&')
    paramsArr.forEach((item) => {
      const [key, value] = item.split('=')
      params[key] = value
    })
  }
  return params
}

/** 节流函数 */
export function throttle(func, delay = 150) {
  let lastTime = 0
  return function (...args) {
    const now = new Date().getTime()
    if (now - lastTime >= delay) {
      func.apply(this, args)
      lastTime = now
    }
  }
}

/** 节流函数，保证一次尾调 */
/** 防抖函数 */
export function debounce<T extends(...args: any[]) => any>(func: T, wait = 240): T {
  let timeout: NodeJS.Timeout | null = null
  let lastCallTime = 0
  let leadingCall = true

  return function (this: any, ...args: any[]) {
    const now = Date.now()

    if (leadingCall) {
      leadingCall = false
      lastCallTime = now
      func.apply(this, args)
    } else if (now - lastCallTime >= wait) {
      lastCallTime = now
      func.apply(this, args)
    } else {
      if (timeout !== null) {
        clearTimeout(timeout)
      }
      timeout = setTimeout(() => {
        lastCallTime = Date.now()
        func.apply(this, args)
      }, wait)
    }
  } as T
}

export function fakerThrottle(func, delay = 150) {
  let lastTime = 0
  let timer
  return function (...args) {
    clearTimeout(timer)
    const now = new Date().getTime()
    if (now - lastTime >= delay) {
      func.apply(this, args)
      lastTime = now
    } else {
      timer = setTimeout(() => {
        func.apply(this, args)
        lastTime = now
      }, delay)
    }
  }
}

/** 根据传值path获取路由参数 */
export const getQueryString = (path: string, name: string) => {
  if (path.indexOf('?') >= 0) {
    const params = path.split('?')
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i')
    const r = params[1].match(reg)
    if (r != null) {
      return unescape(r[2])
    }
  }

  return null
}

/**
 * 获取顶部导航高度
 * @param {number} paddingBottom 自定义header中的paddingBottom
 */
export const getNavHeight = (paddingBottom = 8) => {
  const { top: headerTop, height: headerHeight } = getMenuButtonBoundingClientRect()
  // 计算超出视图时距离顶部的距离
  return pxToRpx(headerTop + headerHeight) + paddingBottom
}

/** rpx转px */
export const rpxToPx = (value = 0): number => {
  return (value / 750) * getSystemInfoSync().windowWidth
}

/** px转rpx */
export const pxToRpx = (value = 0): number => {
  return (value * 750) / getSystemInfoSync().windowWidth
}

/** AES-258-ECB加密 */
export const AesEncrypt = async (data) => {
  const c = await CryptoJS()
  const key = c.enc.Utf8.parse(app.nonceKey)
  const bytes = c.AES.encrypt(data, key, {
    mode: c.mode.ECB,
    padding: c.pad.Pkcs7,
  })
  return bytes.toString()
}

/** 将path?key=value的路由字符串解析为{path,params} */
export const parsePath = (route: string) => {
  if (route.includes('?')) {
    const [path, queryStr] = route.split('?')
    const queryArr = queryStr.split('&')
    const query = {}
    for (let index = 0; index < queryArr.length; index += 1) {
      const [key, value] = queryArr[index].split('=')
      query[key] = value
    }
    return { path, query }
  }
  return { path: route, query: {} }
}

/** 根据时间戳返回知指定的星期几 */
export const getWeek = (timestamp) => {
  const week = new Date(timestamp).getDay()
  const weekArr = ['日', '一', '二', '三', '四', '五', '六']
  return `星期${weekArr[week]}`
}

/** 处理多个路径末尾拼接时多个斜杠的问题, 如果第一个数组是有一个斜杠需要拼接上, 如果最后一个有斜杠则去掉 */
export function joinPath(...paths: (string | number)[]) {
  const path = paths
    .map((path, index) => {
      const newPath = path != null ? `${path}` : ''
      if (index === 0) {
        return newPath.replace(/\/$/, '')
      }
      return newPath.replace(/(^\/|\/$)/g, '')
    })
    .filter((path) => path)
    .join('/')
  if (!path.startsWith('/') && `${paths[0]}`.startsWith('/')) {
    return `/${path}`
  }
  return path
}

/** 转换json字符串并返回 */
export function headerJsonParse(val: any, defVal = undefined): any {
  if (val == null) {
    return defVal
  }
  if (typeof val === 'object') {
    return val
  }
  if (typeof val !== 'string') {
    return defVal
  }
  try {
    return JSON.parse(val)
  } catch (error) {
    // 解析失败，变量不是JSON
    wx.$.collectEvent.event('miniPageJsonError', { error, value: val, key: 'val---headerJsonParse' })
    return defVal
  }
}
// 解密
export function decode(inputs) {
  if (!inputs) {
    return ''
  }
  const keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
  let output = ''
  let chr1
  let chr2
  let chr3
  let enc1
  let enc2
  let enc3
  let enc4
  let i = 0
  const input = String(inputs).replace(/[^A-Za-z0-9\+\/\=]/g, '')
  while (i <= input.length) {
    enc1 = keyStr.indexOf(input.charAt(i++))
    enc2 = keyStr.indexOf(input.charAt(i++))
    enc3 = keyStr.indexOf(input.charAt(i++))
    enc4 = keyStr.indexOf(input.charAt(i++))
    chr1 = (enc1 << 2) | (enc2 >> 4)
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2)
    chr3 = ((enc3 & 3) << 6) | enc4
    output += String.fromCharCode(chr1)
    if (enc3 != 64) {
      output += String.fromCharCode(chr2)
    }
    if (enc4 != 64) {
      output += String.fromCharCode(chr3)
    }
  }
  return String(output)
}

/** json对象排序 */
export function sortObject(obj: any): { sortObj: any, sortStr: string } {
  if (!obj || typeof obj !== 'object') {
    return obj
  }
  const newObj = wx.$.u.deepClone(obj)
  function handler(obj: any): any {
    if (Array.isArray(obj)) { // 如果是数组，对数组元素进行递归排序
      obj.sort()
      return obj.map((item: any) => handler(item))
    }
    if (typeof obj === 'object' && obj !== null) {
      // 如果是对象，对对象的属性进行递归排序
      const sortedObject: { [key: string]: any } = {}
      Object.keys(obj)
        .sort()
        .forEach((key: string) => {
          sortedObject[key] = handler(obj[key])
        })
      return sortedObject
    }
    if (typeof obj === 'string' && !Number.isNaN(Number(obj))) {
      // 如果是字符串表示的数字，转换为实际数字类型
      return Number(obj)
    }
    // 如果是基本类型或null，直接返回
    return obj
  }
  const sortObj = handler(newObj)
  const sortStr = JSON.stringify(sortObj)
  return { sortObj, sortStr }
}

/** 判断两个对象的值是否一样 */
export function isEqualObj(obj1: any, obj2: any): boolean {
  const { sortStr: sortStr1 } = sortObject(obj1 || {})
  const { sortStr: sortStr2 } = sortObject(obj2 || {})
  return JSON.stringify(sortStr1) === JSON.stringify(sortStr2)
}

/** 判断是否是假值 包括 0 和 "0" */
export function isValNull(val) {
  if (!val || val == null) {
    return true
  }
  if (val == '0' || val == 0) {
    return true
  }
  return false
}

/** 指定时间后隐藏loading */
export function hideLoadTime(time = 1500) {
  setTimeout(() => {
    wx.hideLoading()
  }, time)
}

/** 处理特殊字符的转义报错问题，返回转义后的字符串 */
export function escapeStr(str: string): string {
  if (typeof str !== 'string') {
    return str
  }
  return str.replace(/([.*+?^=!:${}()|[\]/\\])/g, '\\$1')
}

export function findClosestIndex(target, arr) {
  let minDiff = Infinity
  let closestIndex = -1
  arr.reduce((prev, curr, index) => {
    const diff = Math.abs(target - curr)
    if (diff < minDiff) {
      minDiff = diff
      closestIndex = index
    }
    return curr
  }, 0)
  return closestIndex
}

/** AES/CBC+BASE64加密 */
export const encrypt = async (data, type = 'report') => {
  if (!data) {
    return ''
  }
  const c = await CryptoJS()
  const key = c.enc.Base64.parse(type === 'report' ? REPORT_ENCRYPT_KEY : 'yupaowang^~^see!!&&upss$df@tt')
  const iv = c.enc.Base64.parse(type === 'report' ? REPORT_ENCRYPT_IV : 'ab9^!&ywx*47$@9d')

  const encrypted = c.AES.encrypt(data, key, {
    iv,
    mode: c.mode.CBC,
    padding: c.pad.Pkcs7,
  })
  return encrypted.toString()
}
